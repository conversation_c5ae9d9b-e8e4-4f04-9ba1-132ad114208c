<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Test Page</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #f9fafb;
            --text-color: #1f2937;
            --border-color: #e5e7eb;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            color: var(--text-color);
            line-height: 1.5;
        }

        header {
            background-color: white;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color);
            text-decoration: none;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        nav a {
            text-decoration: none;
            color: var(--text-color);
            font-weight: 500;
            transition: color 0.2s;
        }

        nav a:hover {
            color: var(--primary-color);
        }

        .hero {
            background-color: var(--secondary-color);
            padding: 4rem 1rem;
            text-align: center;
        }

        .hero-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.125rem;
            color: #4b5563;
            margin-bottom: 2rem;
        }

        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .btn:hover {
            background-color: #4338ca;
        }

        .features {
            padding: 4rem 1rem;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .features h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 3rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1.5rem;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .feature-card h3 {
            margin-bottom: 0.75rem;
            font-size: 1.25rem;
        }

        .feature-card p {
            color: #6b7280;
        }

        .cta {
            background-color: var(--primary-color);
            color: white;
            padding: 4rem 1rem;
            text-align: center;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .cta h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .cta p {
            margin-bottom: 2rem;
            font-size: 1.125rem;
            opacity: 0.9;
        }

        .cta .btn {
            background-color: white;
            color: var(--primary-color);
        }

        .cta .btn:hover {
            background-color: #f3f4f6;
        }

        footer {
            background-color: #1f2937;
            color: #9ca3af;
            padding: 2rem 1rem;
        }

        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .footer-links {
            display: flex;
            gap: 1.5rem;
        }

        .footer-links a {
            color: #9ca3af;
            text-decoration: none;
            transition: color 0.2s;
        }

        .footer-links a:hover {
            color: white;
        }

        @media (max-width: 768px) {
            nav ul {
                gap: 1rem;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .footer-container {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>

<body>
    <header>
        <div class="header-container">
            <a href="#" class="logo">TestCompany</a>
            <nav>
                <ul>
                    <li><a href="#">Home</a></li>
                    <li><a href="#">Features</a></li>
                    <li><a href="#">Pricing</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="hero-container">
            <h1>Your Complete Business Solution</h1>
            <p>Streamline operations, boost productivity, and grow your business with our all-in-one platform.</p>
            <a href="#" class="btn">Get Started Today</a>
        </div>
    </section>

    <section class="features">
        <div class="features-container">
            <h2>Why Choose Our Platform</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>Easy Integration</h3>
                    <p>Integrate seamlessly with your existing tools and workflows without any hassle.</p>
                </div>
                <div class="feature-card">
                    <h3>Real-time Analytics</h3>
                    <p>Get powerful insights into your business performance with our advanced analytics.</p>
                </div>
                <div class="feature-card">
                    <h3>24/7 Support</h3>
                    <p>Our dedicated support team is always available to help you with any issues.</p>
                </div>
                <div class="feature-card">
                    <h3>Secure & Reliable</h3>
                    <p>Enterprise-grade security and 99.9% uptime guarantee for your peace of mind.</p>
                </div>
                <div class="feature-card">
                    <h3>Customizable</h3>
                    <p>Tailor our platform to fit your specific business needs and requirements.</p>
                </div>
                <div class="feature-card">
                    <h3>Scalable Solution</h3>
                    <p>Grow your business without worrying about outgrowing your tools and systems.</p>
                </div>
            </div>
        </div>
    </section>

    <section class="cta">
        <div class="cta-container">
            <h2>Ready to Transform Your Business?</h2>
            <p>Join thousands of businesses that trust our platform to drive growth and success.</p>
            <a href="#" class="btn">Start Free Trial</a>
        </div>
    </section>

    <footer>
        <div class="footer-container">
            <div>© 2025 TestCompany. All rights reserved.</div>
            <div class="footer-links">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <a href="#">Sitemap</a>
            </div>
        </div>
    </footer>

    <!-- Widget embed code will be inserted here -->
    <script>
        // Function to get URL parameters
        function getURLParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // Get widget ID and environment from URL parameters
        const widgetId = getURLParameter('widgetId') || 'demo_widget_123';
        const environment = getURLParameter('env') || 'development';

        // Try to parse config from URL if provided
        let widgetConfig;
        try {
            const configParam = getURLParameter('config');
            widgetConfig = configParam ? JSON.parse(decodeURIComponent(configParam)) : null;
        } catch (e) {
            console.error('Error parsing widget config:', e);
            widgetConfig = null;
        }

        // Default configuration (used if no config is provided via URL)
        const defaultConfig = {
            appearance: {
                primaryColor: "#3b82f6",
                secondaryColor: "#eff6ff",
                textColor: "#1f2937",
                borderRadius: 12,
                shadow: "md",
                darkMode: false,
                glassmorphism: false
            },
            behavior: {
                position: "bottom-right",
                showOnLoad: true,
                showAfterSeconds: 3,
                showAfterScroll: 50,
                showOnExit: false
            },
            content: {
                headerTitle: "Chat with us",
                headerSubtitle: "We typically reply within minutes",
                welcomeMessage: "Hi there! How can I help you today?",
                inputPlaceholder: "Type your message..."
            }
        };

        // Dynamically create and inject the widget script
        (function (w, d, s, o, f, js, fjs) {
            w['ChatWidget'] = o; w[o] = w[o] || function () { (w[o].q = w[o].q || []).push(arguments) };
            w[o].l = 1 * new Date(); js = d.createElement(s), fjs = d.getElementsByTagName(s)[0];
            js.id = o; js.src = f; js.async = 1; fjs.parentNode.insertBefore(js, fjs);

            // Log that the widget is loading
            console.log(`Loading widget ${widgetId} from ${environment} environment`);
        }(window, document, 'script', 'chatWidget', `https://${environment}.chatadmin.com/widget/${widgetId}.js`));

        // Initialize with configuration from URL or default
        chatWidget('init', widgetConfig || defaultConfig);
    </script>
</body>

</html>