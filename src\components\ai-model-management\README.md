# AI Model Management Components

A comprehensive suite of React components for managing machine learning models, including training, deployment, monitoring, and version control.

## 🚀 Features

- **Model Overview**: Browse and manage all your ML models
- **Model Cards**: Individual model information and status
- **Training Form**: Create and configure new models
- **Performance Metrics**: Real-time analytics and insights
- **Version History**: Track model versions and changes
- **Deployment Settings**: Configure and deploy models
- **Advanced Filtering**: Search and filter models
- **Sidebar Navigation**: Organized navigation structure

## 📁 Components

### Core Components

- **`ModelCard`** - Individual model display with status, metrics, and actions
- **`ModelFilters`** - Advanced filtering and search functionality
- **`ModelMetricsCard`** - Performance analytics and visualizations
- **`ModelTrainingForm`** - Form for creating and training new models
- **`ModelVersionHistory`** - Version control and comparison tools
- **`ModelDeploymentSettings`** - Deployment configuration and management

### Layout Components

- **`ModelManagementSidebar`** - Navigation sidebar for model management
- **`ModelManagementLayout`** - Complete layout with sidebar and content area

## 🎯 Usage

### Basic Usage

```tsx
import {
  ModelCard,
  ModelFilters,
  ModelMetricsCard,
  ModelTrainingForm,
  ModelVersionHistory,
  ModelDeploymentSettings,
} from "@/components/ai-model-management";

// Use individual components
<ModelCard
  id="1"
  name="Sentiment Analyzer"
  description="NLP model for sentiment analysis"
  status="active"
  type="Classification"
  version="v2.1.0"
  lastUpdated="2024-01-15"
  accuracy={94.2}
  onSelect={(id) => console.log("Selected:", id)}
/>
```

### Complete Layout

```tsx
import { ModelManagementLayout } from "@/components/ai-model-management";

// Full layout with sidebar navigation
<ModelManagementLayout
  defaultComponent="overview"
  showSidebar={true}
  sidebarCollapsible={true}
/>
```

### Sidebar Only

```tsx
import { ModelManagementSidebar } from "@/components/ai-model-management";

<ModelManagementSidebar
  onComponentSelect={(component) => setActiveComponent(component)}
  activeComponent={activeComponent}
  showSearch={true}
  showActions={true}
/>
```

## 🛠️ Props

### ModelCard Props

```tsx
interface ModelCardProps {
  id: string;
  name: string;
  description: string;
  status: "active" | "training" | "error" | "draft";
  type: string;
  version: string;
  lastUpdated: string;
  accuracy?: number;
  trainingProgress?: number;
  onSelect: (id: string) => void;
}
```

### ModelManagementLayout Props

```tsx
interface ModelManagementLayoutProps {
  defaultComponent?: string;
  showSidebar?: boolean;
  sidebarCollapsible?: boolean;
}
```

### ModelManagementSidebar Props

```tsx
interface ModelManagementSidebarProps {
  onComponentSelect?: (component: string) => void;
  activeComponent?: string;
  showSearch?: boolean;
  showActions?: boolean;
}
```

## 🎨 Styling

All components use Tailwind CSS and Shadcn UI components for consistent styling. The design follows professional business standards with:

- Clean, minimal interface
- Consistent color scheme
- Responsive design
- Accessible components
- Professional animations

## 🔧 Customization

### Theme Colors

The components respect the application's theme and use CSS variables for colors:

- `--primary` - Primary brand color
- `--secondary` - Secondary color
- `--muted` - Muted text color
- `--background` - Background color
- `--border` - Border color

### Component Variants

Most components support different variants and sizes through props:

```tsx
<Button variant="outline" size="sm">
  Action
</Button>

<Badge variant="secondary">
  Status
</Badge>
```

## 📱 Responsive Design

All components are fully responsive and work across:

- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🧪 Testing

### Demo Page

Visit `/model-components-demo` to see all components in action with mock data.

### Individual Testing

Each component can be tested individually:

```tsx
// Test with mock data
const mockModel = {
  id: "1",
  name: "Test Model",
  description: "Test description",
  status: "active",
  type: "Classification",
  version: "v1.0.0",
  lastUpdated: "2024-01-15",
  accuracy: 95.0,
};

<ModelCard {...mockModel} onSelect={console.log} />
```

## 🚀 Routes

- `/ml-model-management` - Full model management interface
- `/model-components-demo` - Component demonstration page

## 📦 Dependencies

- React 18+
- React Router DOM
- Tailwind CSS
- Shadcn UI
- Lucide React (icons)
- Framer Motion (animations)

## 🤝 Contributing

When adding new components:

1. Follow the existing naming convention
2. Use TypeScript interfaces for props
3. Include proper JSDoc comments
4. Add to the index.ts export file
5. Update this README

## 📄 License

Part of the AI Insight Conversations application.
