import React from "react";
import { ModelManagementLayout } from "@/components/ai-model-management";

/**
 * ML Model Management Page
 * 
 * This page provides a comprehensive interface for managing machine learning models
 * including training, deployment, monitoring, and version control.
 * 
 * Features:
 * - Model overview and cards
 * - Training form for new models
 * - Performance metrics and analytics
 * - Version history and comparison
 * - Deployment settings and management
 * - Advanced filtering and search
 * - Monitoring and resource usage
 */
const MLModelManagement: React.FC = () => {
  return (
    <div className="min-h-screen bg-background">
      <ModelManagementLayout
        defaultComponent="overview"
        showSidebar={true}
        sidebarCollapsible={true}
      />
    </div>
  );
};

export default MLModelManagement;
