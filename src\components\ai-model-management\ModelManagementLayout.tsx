import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Sidebar,
  SidebarContent,
  SidebarProvider
} from "@/components/ui/sidebar";
import { ModelManagementSidebar } from "./ModelManagementSidebar";
import ModelCard from "./ModelCard";
import ModelFilters from "./ModelFilters";
import ModelMetricsCard from "./ModelMetricsCard";
import ModelTrainingForm from "./ModelTrainingForm";
import ModelVersionHistory from "./ModelVersionHistory";
import ModelDeploymentSettings from "./ModelDeploymentSettings";
import {
  Brain,
  Settings,
  BarChart3,
  Layers,
  GitBranch,
  Rocket,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

interface ModelManagementLayoutProps {
  defaultComponent?: string;
  showSidebar?: boolean;
  sidebarCollapsible?: boolean;
}

export const ModelManagementLayout: React.FC<ModelManagementLayoutProps> = ({
  defaultComponent = "overview",
  showSidebar = true,
  sidebarCollapsible = true,
}) => {
  const [activeComponent, setActiveComponent] = useState(defaultComponent);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Filter states
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");

  // Mock data for demonstration
  const mockModels = [
    {
      id: "1",
      name: "Customer Sentiment Analyzer",
      description: "Advanced NLP model for analyzing customer feedback sentiment",
      status: "active" as const,
      type: "Classification",
      version: "v2.1.0",
      lastUpdated: "2024-01-15",
      accuracy: 94.2,
    },
    {
      id: "2",
      name: "Product Recommendation Engine",
      description: "ML model for personalized product recommendations",
      status: "training" as const,
      type: "Recommendation",
      version: "v1.3.0",
      lastUpdated: "2024-01-14",
      trainingProgress: 75,
    },
    {
      id: "3",
      name: "Fraud Detection Model",
      description: "Real-time fraud detection for financial transactions",
      status: "error" as const,
      type: "Classification",
      version: "v1.0.2",
      lastUpdated: "2024-01-13",
      accuracy: 89.7,
    },
  ];

  const mockVersions = [
    {
      id: "v2.1.0",
      version: "v2.1.0",
      date: "2024-01-15",
      accuracy: 94.2,
      status: "active" as const,
      size: "245 MB",
      commitMessage: "Improved accuracy with enhanced feature engineering",
    },
    {
      id: "v2.0.0",
      version: "v2.0.0",
      date: "2024-01-10",
      accuracy: 92.8,
      status: "archived" as const,
      size: "238 MB",
      commitMessage: "Major architecture update with transformer layers",
    },
  ];

  const handleComponentSelect = (component: string) => {
    setActiveComponent(component);
  };

  const handleModelSelect = (modelId: string) => {
    console.log("Selected model:", modelId);
  };

  const clearFilters = () => {
    setSearch("");
    setStatusFilter("all");
    setTypeFilter("all");
  };

  const renderActiveComponent = () => {
    switch (activeComponent) {
      case "overview":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-slate-800">Model Overview</h2>
                <p className="text-slate-600 mt-1">Manage and monitor your AI models</p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="gap-1">
                  <Brain className="h-3 w-3" />
                  {mockModels.length} Models
                </Badge>
                <Badge variant="outline" className="gap-1">
                  <BarChart3 className="h-3 w-3" />
                  Active Monitoring
                </Badge>
              </div>
            </div>
            <ModelFilters
              search={search}
              setSearch={setSearch}
              statusFilter={statusFilter}
              setStatusFilter={setStatusFilter}
              typeFilter={typeFilter}
              setTypeFilter={setTypeFilter}
              clearFilters={clearFilters}
            />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mockModels.map((model) => (
                <ModelCard
                  key={model.id}
                  {...model}
                  onSelect={handleModelSelect}
                />
              ))}
            </div>
          </div>
        );

      case "model-cards":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-slate-800">Model Cards</h2>
                <p className="text-slate-600 mt-1">Browse and manage individual model cards</p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mockModels.map((model) => (
                <ModelCard
                  key={model.id}
                  {...model}
                  onSelect={handleModelSelect}
                />
              ))}
            </div>
          </div>
        );

      case "training-form":
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-800">Model Training</h2>
              <p className="text-slate-600 mt-1">Create and configure new AI models</p>
            </div>
            <ModelTrainingForm
              onSubmit={(data) => console.log("Training data:", data)}
            />
          </div>
        );

      case "metrics":
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-800">Model Metrics</h2>
              <p className="text-slate-600 mt-1">Performance analytics and insights</p>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <ModelMetricsCard modelId="1" />
              <ModelMetricsCard modelId="2" />
            </div>
          </div>
        );

      case "version-history":
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-800">Version History</h2>
              <p className="text-slate-600 mt-1">Track model versions and changes</p>
            </div>
            <ModelVersionHistory
              versions={mockVersions}
              onCompare={(versions) => console.log("Compare versions:", versions)}
              onRevert={(versionId) => console.log("Revert to version:", versionId)}
              onDownload={(versionId) => console.log("Download version:", versionId)}
            />
          </div>
        );

      case "deployment":
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-800">Model Deployment</h2>
              <p className="text-slate-600 mt-1">Deploy and manage model instances</p>
            </div>
            <ModelDeploymentSettings
              modelId="1"
              onSave={(settings) => console.log("Save settings:", settings)}
              onDeploy={() => console.log("Deploy model")}
            />
          </div>
        );

      case "filters":
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-800">Advanced Filters</h2>
              <p className="text-slate-600 mt-1">Filter and search through your models</p>
            </div>
            <Card>
              <CardHeader>
                <CardTitle>Model Filters</CardTitle>
              </CardHeader>
              <CardContent>
                <ModelFilters
                  search={search}
                  setSearch={setSearch}
                  statusFilter={statusFilter}
                  setStatusFilter={setStatusFilter}
                  typeFilter={typeFilter}
                  setTypeFilter={setTypeFilter}
                  clearFilters={clearFilters}
                />
              </CardContent>
            </Card>
          </div>
        );

      default:
        return (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-800 mb-2">Component Not Found</h3>
              <p className="text-slate-600">The selected component is not available.</p>
            </div>
          </div>
        );
    }
  };

  if (!showSidebar) {
    return (
      <div className="p-6">
        {renderActiveComponent()}
      </div>
    );
  }

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full bg-background">
        <Sidebar
          variant="sidebar"
          collapsible={sidebarCollapsible ? "icon" : "none"}
          className="border-r"
        >
          <SidebarContent>
            <ModelManagementSidebar
              onComponentSelect={handleComponentSelect}
              activeComponent={activeComponent}
              showSearch={!sidebarCollapsed}
              showActions={!sidebarCollapsed}
            />
          </SidebarContent>
        </Sidebar>

        <main className="flex-1 overflow-auto">
          <div className="p-6">
            {renderActiveComponent()}
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
};
