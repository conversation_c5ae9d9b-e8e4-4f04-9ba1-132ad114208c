import React, { useState } from "react";
import { cn } from "@/lib/utils";
import {
  MessageSquare,
  X,
  Maximize2,
  Minimize2,
  Send,
  User,
  Bot,
} from "lucide-react";


interface ModernWidgetPreviewProps {
  config: any;
  deviceType?: 'desktop' | 'tablet' | 'mobile';
}

const ModernWidgetPreview: React.FC<ModernWidgetPreviewProps> = ({
  config,
  deviceType = 'desktop',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState([
    {
      type: "bot",
      content: config.content.welcomeMessage,
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");

  const toggleWidget = () => {
    setIsOpen(!isOpen);
    if (isExpanded && !isOpen) {
      setIsExpanded(false);
    }
  };

  const toggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    // Add user message
    const newMessages = [
      ...messages,
      { type: "user", content: inputValue, timestamp: new Date() },
    ];
    setMessages(newMessages);
    setInputValue("");

    // Simulate bot response after a delay
    setTimeout(() => {
      setMessages([
        ...newMessages,
        {
          type: "bot",
          content:
            "Thank you for your message! How else can I assist you today?",
          timestamp: new Date(),
        },
      ]);
    }, 1000);
  };

  // Apply theme colors
  const primaryColor = config.appearance.primaryColor;
  const borderRadius = `${config.appearance.borderRadius}px`;
  const iconSize = `${config.appearance.chatIconSize}px`;
  const fontFamily = config.appearance.fontFamily || "system-ui";

  // Device-responsive sizing
  const deviceSizing = {
    desktop: {
      widgetWidth: "w-80",
      widgetHeight: "h-96",
      iconSize: iconSize,
      fontSize: "text-sm",
      padding: "p-4",
      gap: "gap-4",
      position: "bottom-4 right-4"
    },
    tablet: {
      widgetWidth: "w-72",
      widgetHeight: "h-80",
      iconSize: `${Math.max(32, parseInt(iconSize) * 0.9)}px`,
      fontSize: "text-sm",
      padding: "p-3",
      gap: "gap-3",
      position: "bottom-4 right-4"
    },
    mobile: {
      widgetWidth: "w-64",
      widgetHeight: "h-72",
      iconSize: `${Math.max(28, parseInt(iconSize) * 0.8)}px`,
      fontSize: "text-xs",
      padding: "p-2",
      gap: "gap-2",
      position: "bottom-4 right-4"
    }
  };

  const currentSizing = deviceSizing[deviceType];

  // Adjust widget size for mobile/tablet to fit in device frame
  const getWidgetConstraints = () => {
    if (deviceType === 'mobile') {
      return {
        maxWidth: '280px',
        maxHeight: '320px',
        width: '280px',
        height: '320px'
      };
    }
    if (deviceType === 'tablet') {
      return {
        maxWidth: '320px',
        maxHeight: '400px',
        width: '320px',
        height: '400px'
      };
    }
    return {
      maxWidth: '320px',
      maxHeight: '384px',
      width: '320px',
      height: '384px'
    };
  };

  const widgetConstraints = getWidgetConstraints();



  // Animation classes
  const animationClasses =
    {
      fade: "transition-opacity duration-300",
      slide: "transition-transform duration-300",
      bounce: "animate-bounce",
      none: "",
    }[config.behavior.animation] || "fade";

  // Apply theme mode
  const themeMode = config.appearance.theme || 'light';
  const isDarkMode = themeMode === 'dark' || (themeMode === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  // Theme-aware colors
  const backgroundColors = {
    light: 'bg-slate-50',
    dark: 'bg-slate-900'
  };

  const mockContentColors = {
    light: 'bg-white',
    dark: 'bg-slate-800'
  };

  const browserBarColors = {
    light: 'bg-slate-200',
    dark: 'bg-slate-700'
  };

  return (
    <div className={cn(
      "relative w-full h-[500px] rounded-lg border overflow-hidden transition-colors duration-300",
      isDarkMode ? backgroundColors.dark : backgroundColors.light
    )}>
      {/* Mock browser frame */}
      <div className={cn(
        "h-8 flex items-center px-3 gap-1.5 transition-colors duration-300",
        isDarkMode ? browserBarColors.dark : browserBarColors.light
      )}>
        <div className="w-3 h-3 rounded-full bg-red-400"></div>
        <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
        <div className="w-3 h-3 rounded-full bg-green-400"></div>
        <div className={cn(
          "ml-4 h-5 w-64 rounded-md transition-colors duration-300",
          isDarkMode ? "bg-slate-600" : "bg-white"
        )}></div>
      </div>

      {/* Mock website content */}
      <div className="p-4 h-[calc(100%-2rem)] relative">
        <div className={cn(
          "w-full h-12 rounded-md shadow-sm mb-4 transition-colors duration-300",
          isDarkMode ? mockContentColors.dark : mockContentColors.light
        )}></div>
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className={cn(
            "h-32 rounded-md shadow-sm transition-colors duration-300",
            isDarkMode ? mockContentColors.dark : mockContentColors.light
          )}></div>
          <div className={cn(
            "h-32 rounded-md shadow-sm transition-colors duration-300",
            isDarkMode ? mockContentColors.dark : mockContentColors.light
          )}></div>
          <div className={cn(
            "h-32 rounded-md shadow-sm transition-colors duration-300",
            isDarkMode ? mockContentColors.dark : mockContentColors.light
          )}></div>
        </div>
        <div className={cn(
          "w-full h-64 rounded-md shadow-sm mb-4 transition-colors duration-300",
          isDarkMode ? mockContentColors.dark : mockContentColors.light
        )}></div>
        <div className={cn(
          "w-3/4 h-8 rounded-md shadow-sm mb-4 transition-colors duration-300",
          isDarkMode ? mockContentColors.dark : mockContentColors.light
        )}></div>
        <div className={cn(
          "w-full h-32 rounded-md shadow-sm transition-colors duration-300",
          isDarkMode ? mockContentColors.dark : mockContentColors.light
        )}></div>

        {/* Widget button */}
        <div
          className={cn(
            "absolute shadow-lg cursor-pointer",
            currentSizing.position,
            animationClasses,
          )}
          onClick={toggleWidget}
          style={{
            fontFamily,
            zIndex: 999,
          }}
        >
          {!isOpen ? (
            <div
              className="rounded-full flex items-center justify-center p-3 text-white"
              style={{
                backgroundColor: primaryColor,
                width: currentSizing.iconSize,
                height: currentSizing.iconSize,
                borderRadius:
                  config.appearance.theme === "modern" ? "12px" : "50%",
              }}
            >
              <MessageSquare size={parseInt(currentSizing.iconSize) * 0.5} />
            </div>
          ) : (
            <div
              className={cn(
                "rounded-lg shadow-lg overflow-hidden flex flex-col transition-colors duration-300",
                isExpanded ? "fixed inset-4 h-auto" : "",
                isDarkMode ? "bg-slate-800 text-white" : "bg-white text-slate-900"
              )}
              style={{
                borderRadius,
                border: `1px solid ${primaryColor}20`,
                width: isExpanded ? 'auto' : widgetConstraints.width,
                height: isExpanded ? 'auto' : widgetConstraints.height,
                maxWidth: widgetConstraints.maxWidth,
                maxHeight: widgetConstraints.maxHeight
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div
                className="p-4 flex items-center justify-between"
                style={{ backgroundColor: primaryColor }}
              >
                <div className="flex items-center gap-2">
                  {config.appearance.theme === "modern" && (
                    <div className="bg-white bg-opacity-20 p-1.5 rounded">
                      <MessageSquare size={16} className="text-white" />
                    </div>
                  )}
                  <div>
                    <h3 className="font-medium text-sm text-white">
                      {config.content.headerTitle}
                    </h3>
                    <p className="text-xs text-white text-opacity-80">Online</p>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <button
                    onClick={toggleExpand}
                    className="p-1 rounded-full hover:bg-white hover:bg-opacity-10 text-white"
                  >
                    {isExpanded ? (
                      <Minimize2 size={14} />
                    ) : (
                      <Maximize2 size={14} />
                    )}
                  </button>
                  <button
                    onClick={toggleWidget}
                    className="p-1 rounded-full hover:bg-white hover:bg-opacity-10 text-white"
                  >
                    <X size={14} />
                  </button>
                </div>
              </div>

              {/* Chat area */}
              <div className="flex-1 overflow-y-auto p-4 bg-slate-50">
                {messages.map((message, index) => (
                  <div
                    key={index}
                    className={cn(
                      "mb-4 max-w-[80%]",
                      message.type === "user" ? "ml-auto" : "mr-auto",
                    )}
                  >
                    <div className="flex items-start gap-2">
                      {message.type === "bot" && config.content.showAvatar && (
                        <div
                          className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
                          style={{ backgroundColor: `${primaryColor}20` }}
                        >
                          <Bot size={16} style={{ color: primaryColor }} />
                        </div>
                      )}
                      <div>
                        <div
                          className={cn(
                            "rounded-lg p-3",
                            message.type === "user"
                              ? "bg-primary text-white"
                              : "bg-white border",
                          )}
                          style={
                            message.type === "user"
                              ? { backgroundColor: primaryColor }
                              : {}
                          }
                        >
                          <p className="text-sm">{message.content}</p>
                        </div>
                        <p className="text-xs text-slate-500 mt-1">
                          {message.timestamp.toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>
                      {message.type === "user" && config.content.showAvatar && (
                        <div className="w-8 h-8 rounded-full bg-slate-300 flex items-center justify-center flex-shrink-0">
                          <User size={16} className="text-slate-600" />
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {/* Typing indicator */}
                {config.content.showTypingIndicator &&
                  messages[messages.length - 1]?.type === "user" && (
                    <div className="flex items-center gap-1 mb-4">
                      <div className="w-2 h-2 rounded-full bg-slate-300 animate-bounce"></div>
                      <div
                        className="w-2 h-2 rounded-full bg-slate-300 animate-bounce"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                      <div
                        className="w-2 h-2 rounded-full bg-slate-300 animate-bounce"
                        style={{ animationDelay: "0.4s" }}
                      ></div>
                    </div>
                  )}
              </div>

              {/* Input area */}
              <form
                className={cn(
                  "p-3 border-t flex items-center gap-2 transition-colors duration-300",
                  isDarkMode ? "bg-slate-800 border-slate-600" : "bg-white border-slate-200"
                )}
                onSubmit={handleSendMessage}
              >
                <input
                  type="text"
                  className={cn(
                    "flex-1 border rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-1 transition-colors duration-300",
                    isDarkMode
                      ? "bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                      : "bg-white border-slate-300 text-slate-900 placeholder-slate-500"
                  )}
                  placeholder={config.content.inputPlaceholder}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  style={{
                    borderColor: `${primaryColor}40`,
                  }}
                />
                <button
                  type="submit"
                  className="p-2 rounded-full text-white flex-shrink-0"
                  style={{ backgroundColor: primaryColor }}
                >
                  <Send size={16} />
                </button>
              </form>

              {/* Branding */}
              {config.appearance.theme === "modern" && (
                <div className={cn(
                  "py-1 px-3 border-t text-center transition-colors duration-300",
                  isDarkMode
                    ? "bg-slate-900 border-slate-600"
                    : "bg-slate-50 border-slate-200"
                )}>
                  <p className={cn(
                    "text-[10px] transition-colors duration-300",
                    isDarkMode ? "text-slate-500" : "text-slate-400"
                  )}>
                    Powered by ChatAdmin
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernWidgetPreview;
