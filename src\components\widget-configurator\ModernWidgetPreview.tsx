import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import {
  MessageSquare,
  X,
  Maximize2,
  Minimize2,
  Send,
  User,
  Bot,
  ChevronRight,
  AlertCircle,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react";


interface ModernWidgetPreviewProps {
  config: any;
  deviceType?: 'desktop' | 'tablet' | 'mobile';
  forceOpen?: boolean;
}

const ModernWidgetPreview: React.FC<ModernWidgetPreviewProps> = ({
  config,
  deviceType = 'desktop',
  forceOpen = false,
}) => {
  const [isOpen, setIsOpen] = useState(forceOpen || deviceType === 'mobile' || deviceType === 'tablet');
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState([
    {
      type: "bot",
      content: config.content.welcomeMessage,
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [showPreChatForm, setShowPreChatForm] = useState(config.content.enablePreChatForm);
  const [preChatFormData, setPreChatFormData] = useState<Record<string, string>>({});
  const [preChatFormErrors, setPreChatFormErrors] = useState<Record<string, string>>({});
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedbackValues, setFeedbackValues] = useState<Record<string, any>>({});

  // Update widget state when device type changes
  useEffect(() => {
    setIsOpen(forceOpen || deviceType === 'mobile' || deviceType === 'tablet');
    setIsExpanded(false);
  }, [deviceType, forceOpen]);

  // Handle forceOpen prop changes
  useEffect(() => {
    if (forceOpen) {
      setIsOpen(true);
    }
  }, [forceOpen]);

  // Reset pre-chat form when configuration changes
  useEffect(() => {
    setShowPreChatForm(config.content.enablePreChatForm);
    setPreChatFormData({});
    setPreChatFormErrors({});
  }, [config.content.enablePreChatForm, config.content.preChatFormFields]);

  const toggleWidget = () => {
    setIsOpen(!isOpen);
    if (isExpanded && !isOpen) {
      setIsExpanded(false);
    }
  };

  const toggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    // Add user message
    const newMessages = [
      ...messages,
      { type: "user", content: inputValue, timestamp: new Date() },
    ];
    setMessages(newMessages);
    setInputValue("");

    // Simulate bot response after a delay
    setTimeout(() => {
      setMessages([
        ...newMessages,
        {
          type: "bot",
          content:
            "Thank you for your message! How else can I assist you today?",
          timestamp: new Date(),
        },
      ]);
    }, 1000);
  };

  // Handle pre-chat form input changes
  const handlePreChatInputChange = (fieldId: string, value: string) => {
    setPreChatFormData(prev => ({
      ...prev,
      [fieldId]: value
    }));

    // Clear the error for this field if there was one
    if (preChatFormErrors[fieldId]) {
      setPreChatFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }
  };

  // Submit pre-chat form
  const handlePreChatFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const errors: Record<string, string> = {};
    let hasErrors = false;

    config.content.preChatFormFields?.forEach(field => {
      if (field.required && (!preChatFormData[field.id] || preChatFormData[field.id].trim() === '')) {
        errors[field.id] = `${field.label} is required`;
        hasErrors = true;
      }

      // Simple email validation
      if (field.type === 'email' && preChatFormData[field.id] && !/^\S+@\S+\.\S+$/.test(preChatFormData[field.id])) {
        errors[field.id] = 'Please enter a valid email address';
        hasErrors = true;
      }
    });

    if (hasErrors) {
      setPreChatFormErrors(errors);
      return;
    }

    // If validation passes, hide the form and start the chat
    setShowPreChatForm(false);

    // Add a simulated bot response thanking for the info
    setTimeout(() => {
      setMessages(prev => [
        ...prev,
        {
          type: "bot",
          content: `Thank you for providing your information! How can I help you today?`,
          timestamp: new Date()
        }
      ]);
    }, 1000);
  };

  // Handle feedback submission
  const handleFeedbackSubmit = (feedbackId: string, value: any) => {
    setFeedbackValues(prev => ({
      ...prev,
      [feedbackId]: value
    }));

    // Hide feedback after submission
    setTimeout(() => {
      setShowFeedback(false);

      // Add a thank you message
      setMessages(prev => [
        ...prev,
        {
          type: "bot",
          content: "Thank you for your feedback!",
          timestamp: new Date()
        }
      ]);
    }, 500);
  };

  // Apply theme colors
  const primaryColor = config.appearance.primaryColor;
  const borderRadius = `${config.appearance.borderRadius}px`;
  const iconSize = `${config.appearance.chatIconSize}px`;
  const fontFamily = config.appearance.fontFamily || "system-ui";

  // Extract typography settings with defaults
  const fontSize = config.appearance.fontSize || "medium";
  const fontWeight = config.appearance.fontWeight || "normal";
  const textColor = config.appearance.textColor || "#333333";
  const headerTextColor = config.appearance.headerTextColor || "#ffffff";

  // Extract layout settings with defaults
  const headerStyle = config.appearance.headerStyle || "solid";
  const buttonStyle = config.appearance.buttonStyle || "rounded";
  const gradientEnabled = config.appearance.gradientEnabled || false;
  const shadowIntensity = config.appearance.shadowIntensity || 2;
  const backgroundOpacity = config.appearance.backgroundOpacity || 100;

  // Font size mapping
  const fontSizeMap = {
    small: {
      header: "text-xs",
      message: "text-xs",
      input: "text-xs",
    },
    medium: {
      header: "text-sm",
      message: "text-sm",
      input: "text-sm",
    },
    large: {
      header: "text-base",
      message: "text-base",
      input: "text-base",
    }
  };

  // Font weight mapping
  const fontWeightMap = {
    light: "font-light",
    normal: "font-normal",
    medium: "font-medium",
    semibold: "font-semibold",
    bold: "font-bold"
  };

  // Get font classes
  const getTypographyClasses = (element) => {
    const size = fontSizeMap[fontSize] || fontSizeMap.medium;
    const weight = fontWeightMap[fontWeight] || fontWeightMap.normal;
    return `${size[element]} ${weight}`;
  };

  // Device-responsive sizing
  const deviceSizing = {
    desktop: {
      widgetWidth: "w-80",
      widgetHeight: "h-96",
      iconSize: iconSize,
      fontSize: "text-sm",
      padding: "p-4",
      gap: "gap-4",
      position: "bottom-4 right-4"
    },
    tablet: {
      widgetWidth: "w-72",
      widgetHeight: "h-80",
      iconSize: `${Math.max(48, parseInt(iconSize) * 1.1)}px`,
      fontSize: "text-sm",
      padding: "p-3",
      gap: "gap-3",
      position: "bottom-4 right-4"
    },
    mobile: {
      widgetWidth: "w-64",
      widgetHeight: "h-72",
      iconSize: `${Math.max(48, parseInt(iconSize) * 1.1)}px`,
      fontSize: "text-sm",
      padding: "p-3",
      gap: "gap-3",
      position: "bottom-3 right-3"
    }
  };

  const currentSizing = deviceSizing[deviceType];

  // Adjust widget size for mobile/tablet to fit in device frame
  const getWidgetConstraints = () => {
    if (deviceType === 'mobile') {
      return {
        maxWidth: '95%',
        maxHeight: '90%',
        width: '320px',
        height: '500px'
      };
    }
    if (deviceType === 'tablet') {
      return {
        maxWidth: '90%',
        maxHeight: '85%',
        width: '480px',
        height: '600px'
      };
    }
    return {
      maxWidth: '320px',
      maxHeight: '384px',
      width: '320px',
      height: '384px'
    };
  };

  const widgetConstraints = getWidgetConstraints();

  // Animation classes
  const animationClasses =
    {
      fade: "transition-opacity duration-300",
      slide: "transition-transform duration-300",
      bounce: "animate-bounce",
      none: "",
    }[config.behavior.animation] || "fade";

  // Apply theme mode
  const themeMode = config.appearance.theme || 'light';
  const isDarkMode = themeMode === 'dark' || (themeMode === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  // Theme-aware colors
  const backgroundColors = {
    light: 'bg-slate-50',
    dark: 'bg-slate-900'
  };

  const mockContentColors = {
    light: 'bg-white',
    dark: 'bg-slate-800'
  };

  const browserBarColors = {
    light: 'bg-slate-200',
    dark: 'bg-slate-700'
  };

  // Get button style classes
  const getButtonStyleClasses = (style = buttonStyle) => {
    switch (style) {
      case 'pill':
        return 'rounded-full';
      case 'square':
        return 'rounded-sm';
      case 'minimal':
        return 'rounded-md border-0 shadow-none';
      case 'rounded':
      default:
        return 'rounded-md';
    }
  };

  // Get header style properties
  const getHeaderStyleProps = () => {
    let styles: React.CSSProperties = { backgroundColor: primaryColor, fontFamily };

    if (headerStyle === 'gradient') {
      styles = {
        ...styles,
        backgroundImage: `linear-gradient(to right, ${primaryColor}, ${adjustColor(primaryColor, 30)})`,
      };
    } else if (headerStyle === 'glass') {
      styles = {
        ...styles,
        backgroundColor: `${primaryColor}CC`, // Add transparency
        backdropFilter: 'blur(10px)',
      };
    } else if (headerStyle === 'flat') {
      styles = {
        ...styles,
        boxShadow: 'none',
        borderBottom: '1px solid rgba(0,0,0,0.1)',
      };
    }

    return styles;
  };

  // Get shadow class based on intensity
  const getShadowClass = (intensity = shadowIntensity) => {
    const shadowMap = {
      0: 'shadow-none',
      1: 'shadow-sm',
      2: 'shadow',
      3: 'shadow-md',
      4: 'shadow-lg',
      5: 'shadow-xl',
    };
    return shadowMap[intensity] || 'shadow';
  };

  // Helper function to adjust a color's brightness
  const adjustColor = (color, amount) => {
    // Simple color adjustment for demo purposes
    return color;
  };

  return (
    <div className={cn(
      "relative w-full h-[500px] rounded-lg border overflow-hidden transition-colors duration-300",
      isDarkMode ? backgroundColors.dark : backgroundColors.light
    )}>
      {/* Mock browser frame */}
      <div className={cn(
        "h-8 flex items-center px-3 gap-1.5 transition-colors duration-300",
        isDarkMode ? browserBarColors.dark : browserBarColors.light
      )}>
        <div className="w-3 h-3 rounded-full bg-red-400"></div>
        <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
        <div className="w-3 h-3 rounded-full bg-green-400"></div>
        <div className={cn(
          "ml-4 h-5 w-64 rounded-md transition-colors duration-300",
          isDarkMode ? "bg-slate-600" : "bg-white"
        )}></div>
      </div>

      {/* Mock website content */}
      <div className={cn(
        "h-[calc(100%-2rem)] relative overflow-y-auto",
        deviceType === 'mobile' ? 'p-2' : deviceType === 'tablet' ? 'p-3' : 'p-4'
      )}>
        {/* Header */}
        <div className={cn(
          "w-full rounded-md shadow-sm mb-3 transition-colors duration-300",
          isDarkMode ? mockContentColors.dark : mockContentColors.light,
          deviceType === 'mobile' ? 'h-8' : deviceType === 'tablet' ? 'h-10' : 'h-12'
        )}></div>

        {/* Content Grid */}
        <div className={cn(
          "grid gap-3 mb-3",
          deviceType === 'mobile' ? 'grid-cols-1' : deviceType === 'tablet' ? 'grid-cols-2' : 'grid-cols-3'
        )}>
          <div className={cn(
            "rounded-md shadow-sm transition-colors duration-300",
            isDarkMode ? mockContentColors.dark : mockContentColors.light,
            deviceType === 'mobile' ? 'h-24' : deviceType === 'tablet' ? 'h-28' : 'h-32'
          )}></div>
          {deviceType !== 'mobile' && (
            <div className={cn(
              "rounded-md shadow-sm transition-colors duration-300",
              isDarkMode ? mockContentColors.dark : mockContentColors.light,
              deviceType === 'tablet' ? 'h-28' : 'h-32'
            )}></div>
          )}
          {deviceType === 'desktop' && (
            <div className={cn(
              "h-32 rounded-md shadow-sm transition-colors duration-300",
              isDarkMode ? mockContentColors.dark : mockContentColors.light
            )}></div>
          )}
        </div>

        {/* Main Content */}
        <div className={cn(
          "w-full rounded-md shadow-sm mb-3 transition-colors duration-300",
          isDarkMode ? mockContentColors.dark : mockContentColors.light,
          deviceType === 'mobile' ? 'h-40' : deviceType === 'tablet' ? 'h-48' : 'h-64'
        )}></div>

        {/* Additional Content */}
        <div className={cn(
          "rounded-md shadow-sm mb-3 transition-colors duration-300",
          isDarkMode ? mockContentColors.dark : mockContentColors.light,
          deviceType === 'mobile' ? 'w-full h-6' : deviceType === 'tablet' ? 'w-3/4 h-7' : 'w-3/4 h-8'
        )}></div>

        <div className={cn(
          "w-full rounded-md shadow-sm transition-colors duration-300",
          isDarkMode ? mockContentColors.dark : mockContentColors.light,
          deviceType === 'mobile' ? 'h-24' : deviceType === 'tablet' ? 'h-28' : 'h-32'
        )}></div>

        {/* Extra content for scrolling */}
        {deviceType !== 'desktop' && (
          <>
            <div className={cn(
              "w-full rounded-md shadow-sm mt-3 transition-colors duration-300",
              isDarkMode ? mockContentColors.dark : mockContentColors.light,
              deviceType === 'mobile' ? 'h-20' : 'h-24'
            )}></div>
            <div className={cn(
              "w-2/3 rounded-md shadow-sm mt-3 transition-colors duration-300",
              isDarkMode ? mockContentColors.dark : mockContentColors.light,
              deviceType === 'mobile' ? 'h-6' : 'h-7'
            )}></div>
            {deviceType === 'tablet' && (
              <>
                <div className={cn(
                  "w-full rounded-md shadow-sm mt-3 transition-colors duration-300",
                  isDarkMode ? mockContentColors.dark : mockContentColors.light,
                  'h-32'
                )}></div>
                <div className={cn(
                  "w-4/5 rounded-md shadow-sm mt-3 transition-colors duration-300",
                  isDarkMode ? mockContentColors.dark : mockContentColors.light,
                  'h-8'
                )}></div>
                <div className={cn(
                  "w-full rounded-md shadow-sm mt-3 transition-colors duration-300",
                  isDarkMode ? mockContentColors.dark : mockContentColors.light,
                  'h-20'
                )}></div>
              </>
            )}
          </>
        )}

        {/* Desktop extra content */}
        {deviceType === 'desktop' && (
          <>
            <div className={cn(
              "w-full rounded-md shadow-sm mt-3 transition-colors duration-300",
              isDarkMode ? mockContentColors.dark : mockContentColors.light,
              'h-24'
            )}></div>
            <div className={cn(
              "w-3/4 rounded-md shadow-sm mt-3 transition-colors duration-300",
              isDarkMode ? mockContentColors.dark : mockContentColors.light,
              'h-8'
            )}></div>
          </>
        )}
      </div>

      {/* Widget button */}
      <div
        className={cn(
          "absolute shadow-lg cursor-pointer",
          currentSizing.position,
          animationClasses,
        )}
        onClick={toggleWidget}
        style={{
          fontFamily,
          zIndex: 999,
        }}
      >
        {!isOpen ? (
          <div
            className="rounded-full flex items-center justify-center p-3 text-white"
            style={{
              backgroundColor: primaryColor,
              width: currentSizing.iconSize,
              height: currentSizing.iconSize,
              borderRadius:
                config.appearance.theme === "modern" ? "12px" : "50%",
            }}
          >
            <MessageSquare size={parseInt(currentSizing.iconSize) * 0.5} />
          </div>
        ) : (
          <div
            className={cn(
              "rounded-lg shadow-lg overflow-hidden flex flex-col transition-colors duration-300",
              getShadowClass(),
              isExpanded ? "fixed inset-4 h-auto" : "",
              deviceType === 'mobile' && !isExpanded ? "fixed bottom-3 right-3 left-3" : "",
              deviceType === 'tablet' && !isExpanded ? "fixed bottom-4 right-4 left-4" : "",
              isDarkMode ? "bg-slate-800 text-white" : "bg-white text-slate-900"
            )}
            style={{
              borderRadius: deviceType === 'mobile' ? '12px' : borderRadius,
              border: `1px solid ${primaryColor}20`,
              width: isExpanded ? 'auto' : (deviceType === 'mobile' || deviceType === 'tablet' ? 'auto' : widgetConstraints.width),
              height: isExpanded ? 'auto' : widgetConstraints.height,
              maxWidth: deviceType === 'mobile' || deviceType === 'tablet' ? 'none' : widgetConstraints.maxWidth,
              maxHeight: widgetConstraints.maxHeight,
              opacity: backgroundOpacity / 100,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div
              className={cn(
                "flex items-center justify-between",
                deviceType === 'mobile' ? 'p-2' : deviceType === 'tablet' ? 'p-3' : 'p-4'
              )}
              style={getHeaderStyleProps()}
            >
              <div className="flex items-center gap-2">
                {config.appearance.theme === "modern" && (
                  <div className="bg-white bg-opacity-20 p-1.5 rounded">
                    <MessageSquare size={16} className="text-white" />
                  </div>
                )}
                <div>
                  <h3 className={cn(
                    "font-medium text-white",
                    getTypographyClasses('header')
                  )}>
                    {config.content.headerTitle}
                  </h3>
                  <p className={cn(
                    "text-white text-opacity-80",
                    fontSize === "small" ? "text-[10px]" : "text-xs"
                  )}>Online</p>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={toggleExpand}
                  className="p-1 rounded-full hover:bg-white hover:bg-opacity-10 text-white"
                >
                  {isExpanded ? (
                    <Minimize2 size={14} />
                  ) : (
                    <Maximize2 size={14} />
                  )}
                </button>
                <button
                  onClick={toggleWidget}
                  className="p-1 rounded-full hover:bg-white hover:bg-opacity-10 text-white"
                >
                  <X size={14} />
                </button>
              </div>
            </div>

            {/* Pre-Chat Form */}
            {showPreChatForm && config.content.enablePreChatForm && (
              <div className={cn(
                "flex-1 overflow-y-auto",
                isDarkMode ? "bg-slate-800 text-white" : "bg-white text-slate-900",
                deviceType === 'mobile' ? 'p-3' : 'p-4'
              )}>
                <form onSubmit={handlePreChatFormSubmit}>
                  <div className="mb-4">
                    <h3 className={cn(
                      "font-medium",
                      getTypographyClasses('header')
                    )}>
                      {config.content.preChatFormTitle || "Before we start chatting..."}
                    </h3>
                    <p className={cn(
                      "text-muted-foreground mt-1",
                      getTypographyClasses('message')
                    )}>
                      {config.content.preChatFormSubtitle || "Please provide the following information:"}
                    </p>
                  </div>

                  <div className="space-y-3">
                    {config.content.preChatFormFields?.map((field) => (
                      <div key={field.id} className="space-y-1">
                        <label className={cn(
                          "block font-medium",
                          getTypographyClasses('message')
                        )}>
                          {field.label}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </label>

                        {field.type === 'textarea' ? (
                          <textarea
                            className={cn(
                              "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1",
                              isDarkMode
                                ? "bg-slate-700 border-slate-600 text-white focus:ring-primary/70"
                                : "bg-white border-slate-300 text-slate-900 focus:ring-primary/70",
                              preChatFormErrors[field.id] && "border-red-500",
                              getTypographyClasses('message')
                            )}
                            rows={3}
                            placeholder={field.placeholder}
                            value={preChatFormData[field.id] || ''}
                            onChange={(e) => handlePreChatInputChange(field.id, e.target.value)}
                            style={{ fontFamily }}
                          />
                        ) : field.type === 'select' ? (
                          <select
                            className={cn(
                              "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1",
                              isDarkMode
                                ? "bg-slate-700 border-slate-600 text-white focus:ring-primary/70"
                                : "bg-white border-slate-300 text-slate-900 focus:ring-primary/70",
                              preChatFormErrors[field.id] && "border-red-500",
                              getTypographyClasses('message')
                            )}
                            value={preChatFormData[field.id] || ''}
                            onChange={(e) => handlePreChatInputChange(field.id, e.target.value)}
                            style={{ fontFamily }}
                          >
                            <option value="">Select {field.label.toLowerCase()}</option>
                            {field.options?.map((option) => (
                              <option key={option} value={option}>{option}</option>
                            ))}
                          </select>
                        ) : (
                          <input
                            type={field.type}
                            className={cn(
                              "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1",
                              isDarkMode
                                ? "bg-slate-700 border-slate-600 text-white focus:ring-primary/70"
                                : "bg-white border-slate-300 text-slate-900 focus:ring-primary/70",
                              preChatFormErrors[field.id] && "border-red-500",
                              getTypographyClasses('message')
                            )}
                            placeholder={field.placeholder}
                            value={preChatFormData[field.id] || ''}
                            onChange={(e) => handlePreChatInputChange(field.id, e.target.value)}
                            style={{ fontFamily }}
                          />
                        )}

                        {preChatFormErrors[field.id] && (
                          <p className="text-red-500 text-xs mt-1 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {preChatFormErrors[field.id]}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>

                  <div className="mt-4">
                    <button
                      type="submit"
                      className="w-full py-2 px-4 rounded-md text-white flex items-center justify-center"
                      style={{ backgroundColor: primaryColor, fontFamily }}
                    >
                      Start Chat
                      <ChevronRight className="ml-1 h-4 w-4" />
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Chat area - Only show when pre-chat form is not displayed */}
            {!showPreChatForm && (
              <div className={cn(
                "flex-1 overflow-y-auto bg-slate-50",
                deviceType === 'mobile' ? 'p-2' : deviceType === 'tablet' ? 'p-3' : 'p-4'
              )}>
                {messages.map((message, index) => (
                  <div
                    key={index}
                    className={cn(
                      "max-w-[85%]",
                      deviceType === 'mobile' ? 'mb-2' : 'mb-3',
                      message.type === "user" ? "ml-auto" : "mr-auto",
                    )}
                  >
                    <div className="flex items-start gap-2">
                      {message.type === "bot" && config.content.showAvatar && (
                        <div
                          className={cn(
                            "rounded-full flex items-center justify-center flex-shrink-0",
                            deviceType === 'mobile' ? 'w-6 h-6' : 'w-8 h-8'
                          )}
                          style={{ backgroundColor: `${primaryColor}20` }}
                        >
                          <Bot size={deviceType === 'mobile' ? 12 : 16} style={{ color: primaryColor }} />
                        </div>
                      )}
                      <div>
                        <div
                          className={cn(
                            "rounded-lg",
                            deviceType === 'mobile' ? 'p-2' : 'p-3',
                            message.type === "user"
                              ? "bg-primary text-white"
                              : "bg-white border",
                          )}
                          style={
                            message.type === "user"
                              ? {
                                backgroundColor: primaryColor,
                                fontFamily
                              }
                              : {
                                fontFamily,
                                color: message.type === "bot" ? textColor : undefined
                              }
                          }
                        >
                          <p className={cn(
                            deviceType === 'mobile' ? 'text-xs' : 'text-sm',
                            getTypographyClasses('message')
                          )}>{message.content}</p>
                        </div>
                        <p className="text-xs text-slate-500 mt-1">
                          {message.timestamp.toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>
                      {message.type === "user" && config.content.showAvatar && (
                        <div className={cn(
                          "rounded-full bg-slate-300 flex items-center justify-center flex-shrink-0",
                          deviceType === 'mobile' ? 'w-6 h-6' : 'w-8 h-8'
                        )}>
                          <User size={deviceType === 'mobile' ? 12 : 16} className="text-slate-600" />
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {/* Typing indicator */}
                {config.content.showTypingIndicator &&
                  messages[messages.length - 1]?.type === "user" && (
                    <div className="flex items-center gap-1 mb-4">
                      <div className="w-2 h-2 rounded-full bg-slate-300 animate-bounce"></div>
                      <div
                        className="w-2 h-2 rounded-full bg-slate-300 animate-bounce"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                      <div
                        className="w-2 h-2 rounded-full bg-slate-300 animate-bounce"
                        style={{ animationDelay: "0.4s" }}
                      ></div>
                    </div>
                  )}

                {/* Feedback component - when feedback is enabled and showing */}
                {config.content.enableFeedback && showFeedback && (
                  <div className="border rounded-lg p-3 bg-slate-100 mb-3">
                    <div className="space-y-3">
                      {config.content.feedbackOptions?.map((option) => (
                        <div key={option.id} className="space-y-1">
                          <p className={cn(
                            "font-medium",
                            getTypographyClasses('message')
                          )}>
                            {option.question}
                            {option.required && <span className="text-red-500 ml-1">*</span>}
                          </p>

                          {option.type === 'thumbs' && (
                            <div className="flex gap-3">
                              <button
                                className={cn(
                                  "p-2 rounded-full border",
                                  feedbackValues[option.id] === 'up'
                                    ? "bg-green-100 border-green-500"
                                    : "bg-white border-slate-300 hover:bg-slate-50"
                                )}
                                onClick={() => handleFeedbackSubmit(option.id, 'up')}
                              >
                                <ThumbsUp className="h-5 w-5 text-green-600" />
                              </button>
                              <button
                                className={cn(
                                  "p-2 rounded-full border",
                                  feedbackValues[option.id] === 'down'
                                    ? "bg-red-100 border-red-500"
                                    : "bg-white border-slate-300 hover:bg-slate-50"
                                )}
                                onClick={() => handleFeedbackSubmit(option.id, 'down')}
                              >
                                <ThumbsDown className="h-5 w-5 text-red-600" />
                              </button>
                            </div>
                          )}

                          {/* More feedback types would be rendered here */}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Input area - Only show when pre-chat form is not displayed */}
            {!showPreChatForm && (
              <form
                className={cn(
                  "border-t flex items-center gap-2 transition-colors duration-300",
                  deviceType === 'mobile' ? 'p-2' : deviceType === 'tablet' ? 'p-2.5' : 'p-3',
                  isDarkMode ? "bg-slate-800 border-slate-600" : "bg-white border-slate-200"
                )}
                onSubmit={handleSendMessage}
              >
                <input
                  type="text"
                  className={cn(
                    "flex-1 border rounded-full focus:outline-none focus:ring-1 transition-colors duration-300",
                    deviceType === 'mobile' ? 'px-3 py-1.5' : deviceType === 'tablet' ? 'px-3 py-1.5' : 'px-4 py-2',
                    getTypographyClasses('input'),
                    isDarkMode
                      ? "bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                      : "bg-white border-slate-300 text-slate-900 placeholder-slate-500"
                  )}
                  placeholder={config.content.inputPlaceholder}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  style={{
                    borderColor: `${primaryColor}40`,
                    fontFamily
                  }}
                />
                <button
                  type="submit"
                  className={cn(
                    "text-white flex-shrink-0",
                    getButtonStyleClasses(),
                    deviceType === 'mobile' ? 'p-1.5' : 'p-2'
                  )}
                  style={{ backgroundColor: primaryColor }}
                >
                  <Send size={deviceType === 'mobile' ? 14 : 16} />
                </button>
              </form>
            )}

            {/* Branding */}
            {config.appearance.theme === "modern" && (
              <div className={cn(
                "py-1 px-3 border-t text-center transition-colors duration-300",
                isDarkMode
                  ? "bg-slate-900 border-slate-600"
                  : "bg-slate-50 border-slate-200"
              )}>
                <p className={cn(
                  "text-[10px] transition-colors duration-300",
                  isDarkMode ? "text-slate-500" : "text-slate-400"
                )}
                  style={{ fontFamily }}
                >
                  Powered by ChatAdmin
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ModernWidgetPreview;
