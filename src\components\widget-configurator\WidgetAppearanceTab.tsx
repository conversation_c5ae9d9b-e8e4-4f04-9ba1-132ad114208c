
import React, { useState } from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
  Palette,
  Type,
  Layout,
  Sparkles,
  Eye,
  Copy,
  RotateCcw,
  Wand2,
  Brush,
  Layers,
  Sun,
  Moon,
  Monitor,
  Check,
  ChevronDown,
  ChevronRight
} from "lucide-react";
import { cn } from "@/lib/utils";

interface WidgetAppearanceTabProps {
  config: {
    primaryColor: string;
    secondaryColor: string;
    borderRadius: number;
    chatIconSize: number;
    fontFamily: string;
    gradientEnabled?: boolean;
    shadowIntensity?: number;
    backgroundOpacity?: number;
    headerStyle?: string;
    buttonStyle?: string;
    animationStyle?: string;
    theme?: string;
  };
  onChange: (key: string, value: any) => void;
}

export const WidgetAppearanceTab: React.FC<WidgetAppearanceTabProps> = ({ config, onChange }) => {
  const [activeSection, setActiveSection] = useState("colors");
  const [copiedColor, setCopiedColor] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Enhanced color palettes
  const colorPalettes = {
    brand: [
      { name: "Indigo", color: "#6366f1", class: "bg-indigo-500" },
      { name: "Blue", color: "#3b82f6", class: "bg-blue-500" },
      { name: "Purple", color: "#8b5cf6", class: "bg-purple-500" },
      { name: "Pink", color: "#ec4899", class: "bg-pink-500" },
      { name: "Rose", color: "#f43f5e", class: "bg-rose-500" },
    ],
    professional: [
      { name: "Navy", color: "#1e40af", class: "bg-blue-700" },
      { name: "Slate", color: "#475569", class: "bg-slate-600" },
      { name: "Gray", color: "#6b7280", class: "bg-gray-500" },
      { name: "Zinc", color: "#71717a", class: "bg-zinc-500" },
      { name: "Stone", color: "#78716c", class: "bg-stone-500" },
    ],
    vibrant: [
      { name: "Green", color: "#10b981", class: "bg-emerald-500" },
      { name: "Orange", color: "#f97316", class: "bg-orange-500" },
      { name: "Red", color: "#ef4444", class: "bg-red-500" },
      { name: "Yellow", color: "#eab308", class: "bg-yellow-500" },
      { name: "Teal", color: "#14b8a6", class: "bg-teal-500" },
    ],
    minimal: [
      { name: "Black", color: "#000000", class: "bg-black" },
      { name: "White", color: "#ffffff", class: "bg-white border border-gray-300" },
      { name: "Dark Gray", color: "#374151", class: "bg-gray-700" },
      { name: "Light Gray", color: "#d1d5db", class: "bg-gray-300" },
      { name: "Neutral", color: "#737373", class: "bg-neutral-500" },
    ]
  };

  // Font families with previews
  const fontFamilies = [
    { value: "inter", name: "Inter", preview: "Modern & Clean", class: "font-sans" },
    { value: "roboto", name: "Roboto", preview: "Google's Choice", class: "font-sans" },
    { value: "poppins", name: "Poppins", preview: "Friendly & Round", class: "font-sans" },
    { value: "montserrat", name: "Montserrat", preview: "Elegant & Bold", class: "font-sans" },
    { value: "lato", name: "Lato", preview: "Humanist & Warm", class: "font-sans" },
    { value: "opensans", name: "Open Sans", preview: "Neutral & Clear", class: "font-sans" },
    { value: "playfair", name: "Playfair", preview: "Elegant Serif", class: "font-serif" },
    { value: "system-ui", name: "System UI", preview: "Native Feel", class: "font-system" },
  ];

  // Style presets
  const headerStyles = [
    { value: "solid", name: "Solid", preview: "Clean solid color" },
    { value: "gradient", name: "Gradient", preview: "Smooth color transition" },
    { value: "glass", name: "Glass", preview: "Frosted glass effect" },
    { value: "flat", name: "Flat", preview: "Minimal flat design" },
  ];

  const buttonStyles = [
    { value: "rounded", name: "Rounded", preview: "Modern rounded corners" },
    { value: "square", name: "Square", preview: "Sharp clean edges" },
    { value: "pill", name: "Pill", preview: "Fully rounded ends" },
    { value: "minimal", name: "Minimal", preview: "Subtle appearance" },
  ];

  // Utility functions
  const copyColorToClipboard = (color: string) => {
    navigator.clipboard.writeText(color);
    setCopiedColor(color);
    setTimeout(() => setCopiedColor(null), 2000);
  };

  const generateRandomColor = () => {
    const colors = Object.values(colorPalettes).flat();
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    onChange('primaryColor', randomColor.color);
  };

  const resetToDefaults = () => {
    onChange('primaryColor', '#6366f1');
    onChange('secondaryColor', '#ffffff');
    onChange('borderRadius', 8);
    onChange('chatIconSize', 40);
    onChange('fontFamily', 'inter');
    onChange('gradientEnabled', false);
    onChange('shadowIntensity', 2);
    onChange('backgroundOpacity', 100);
    onChange('headerStyle', 'solid');
    onChange('buttonStyle', 'rounded');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold mb-1 flex items-center gap-2">
            <Brush className="h-5 w-5 text-primary" />
            Visual Appearance
          </h3>
          <p className="text-sm text-muted-foreground">
            Customize colors, typography, and visual effects
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={generateRandomColor}>
            <Wand2 className="h-4 w-4 mr-1" />
            Random
          </Button>
          <Button variant="outline" size="sm" onClick={resetToDefaults}>
            <RotateCcw className="h-4 w-4 mr-1" />
            Reset
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeSection} onValueChange={setActiveSection}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="colors" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Colors
          </TabsTrigger>
          <TabsTrigger value="typography" className="flex items-center gap-2">
            <Type className="h-4 w-4" />
            Typography
          </TabsTrigger>
          <TabsTrigger value="layout" className="flex items-center gap-2">
            <Layout className="h-4 w-4" />
            Layout
          </TabsTrigger>
          <TabsTrigger value="effects" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Effects
          </TabsTrigger>
        </TabsList>

        {/* Colors Tab */}
        <TabsContent value="colors" className="space-y-6 mt-6">
          {/* Primary Color Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <div
                  className="w-4 h-4 rounded-full border"
                  style={{ backgroundColor: config.primaryColor }}
                />
                Primary Color
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Color Palettes */}
                {Object.entries(colorPalettes).map(([category, colors]) => (
                  <div key={category}>
                    <Label className="text-sm font-medium capitalize mb-2 block">
                      {category} Colors
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      {colors.map((colorOption) => (
                        <button
                          key={colorOption.color}
                          className={cn(
                            "group relative w-10 h-10 rounded-lg transition-all hover:scale-105",
                            colorOption.class,
                            config.primaryColor === colorOption.color && "ring-2 ring-offset-2 ring-primary"
                          )}
                          onClick={() => onChange('primaryColor', colorOption.color)}
                          title={`${colorOption.name} - ${colorOption.color}`}
                        >
                          {config.primaryColor === colorOption.color && (
                            <Check className="h-4 w-4 text-white absolute inset-0 m-auto" />
                          )}
                          <span className="sr-only">{colorOption.name}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}

                {/* Custom Color Input */}
                <div className="flex items-center gap-3 pt-2">
                  <div className="relative">
                    <Input
                      type="color"
                      value={config.primaryColor}
                      onChange={(e) => onChange('primaryColor', e.target.value)}
                      className="w-12 h-12 p-1 border-2 rounded-lg cursor-pointer"
                    />
                  </div>
                  <div className="flex-1">
                    <Input
                      type="text"
                      value={config.primaryColor}
                      onChange={(e) => onChange('primaryColor', e.target.value)}
                      placeholder="#6366f1"
                      className="font-mono text-sm"
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyColorToClipboard(config.primaryColor)}
                  >
                    {copiedColor === config.primaryColor ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Secondary Color Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <div
                  className="w-4 h-4 rounded-full border"
                  style={{ backgroundColor: config.secondaryColor }}
                />
                Secondary Color
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Quick secondary color options */}
                <div className="flex flex-wrap gap-2">
                  {['#ffffff', '#f8fafc', '#f1f5f9', '#e2e8f0', '#cbd5e1'].map((color) => (
                    <button
                      key={color}
                      className={cn(
                        "w-10 h-10 rounded-lg border-2 transition-all hover:scale-105",
                        config.secondaryColor === color && "ring-2 ring-offset-2 ring-primary"
                      )}
                      style={{ backgroundColor: color }}
                      onClick={() => onChange('secondaryColor', color)}
                    >
                      {config.secondaryColor === color && (
                        <Check className="h-4 w-4 text-gray-600 absolute inset-0 m-auto" />
                      )}
                    </button>
                  ))}
                </div>

                {/* Custom secondary color */}
                <div className="flex items-center gap-3">
                  <Input
                    type="color"
                    value={config.secondaryColor}
                    onChange={(e) => onChange('secondaryColor', e.target.value)}
                    className="w-12 h-12 p-1 border-2 rounded-lg cursor-pointer"
                  />
                  <Input
                    type="text"
                    value={config.secondaryColor}
                    onChange={(e) => onChange('secondaryColor', e.target.value)}
                    placeholder="#ffffff"
                    className="flex-1 font-mono text-sm"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyColorToClipboard(config.secondaryColor)}
                  >
                    {copiedColor === config.secondaryColor ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Typography Tab */}
        <TabsContent value="typography" className="space-y-6 mt-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Type className="h-4 w-4" />
                Font Selection
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {fontFamilies.map((font) => (
                  <div
                    key={font.value}
                    className={cn(
                      "p-4 border rounded-lg cursor-pointer transition-all hover:border-primary/50",
                      config.fontFamily === font.value && "border-primary bg-primary/5"
                    )}
                    onClick={() => onChange('fontFamily', font.value)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className={cn("font-medium text-lg", font.class)}>
                          {font.name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {font.preview}
                        </div>
                      </div>
                      {config.fontFamily === font.value && (
                        <Check className="h-5 w-5 text-primary" />
                      )}
                    </div>
                    <div className={cn("mt-2 text-sm", font.class)}>
                      The quick brown fox jumps over the lazy dog
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Layout Tab */}
        <TabsContent value="layout" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Size Controls */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Size & Spacing</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label className="mb-3 block">
                    Border Radius: {config.borderRadius}px
                  </Label>
                  <Slider
                    value={[config.borderRadius]}
                    max={24}
                    min={0}
                    step={1}
                    onValueChange={(value) => onChange('borderRadius', value[0])}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>Sharp</span>
                    <span>Rounded</span>
                  </div>
                </div>

                <div>
                  <Label className="mb-3 block">
                    Chat Icon Size: {config.chatIconSize}px
                  </Label>
                  <Slider
                    value={[config.chatIconSize]}
                    max={80}
                    min={24}
                    step={2}
                    onValueChange={(value) => onChange('chatIconSize', value[0])}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>Small</span>
                    <span>Large</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Style Presets */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Style Presets</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="mb-2 block">Header Style</Label>
                  <div className="space-y-2">
                    {headerStyles.map((style) => (
                      <div
                        key={style.value}
                        className={cn(
                          "p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/50",
                          config.headerStyle === style.value && "border-primary bg-primary/5"
                        )}
                        onClick={() => onChange('headerStyle', style.value)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{style.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {style.preview}
                            </div>
                          </div>
                          {config.headerStyle === style.value && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block">Button Style</Label>
                  <div className="space-y-2">
                    {buttonStyles.map((style) => (
                      <div
                        key={style.value}
                        className={cn(
                          "p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/50",
                          config.buttonStyle === style.value && "border-primary bg-primary/5"
                        )}
                        onClick={() => onChange('buttonStyle', style.value)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{style.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {style.preview}
                            </div>
                          </div>
                          {config.buttonStyle === style.value && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Effects Tab */}
        <TabsContent value="effects" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Visual Effects */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  Visual Effects
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Gradient Effects</Label>
                    <p className="text-xs text-muted-foreground">
                      Enable smooth color gradients
                    </p>
                  </div>
                  <Switch
                    checked={config.gradientEnabled || false}
                    onCheckedChange={(checked) => onChange('gradientEnabled', checked)}
                  />
                </div>

                <div>
                  <Label className="mb-3 block">
                    Shadow Intensity: {config.shadowIntensity || 2}
                  </Label>
                  <Slider
                    value={[config.shadowIntensity || 2]}
                    max={5}
                    min={0}
                    step={1}
                    onValueChange={(value) => onChange('shadowIntensity', value[0])}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>None</span>
                    <span>Strong</span>
                  </div>
                </div>

                <div>
                  <Label className="mb-3 block">
                    Background Opacity: {config.backgroundOpacity || 100}%
                  </Label>
                  <Slider
                    value={[config.backgroundOpacity || 100]}
                    max={100}
                    min={70}
                    step={5}
                    onValueChange={(value) => onChange('backgroundOpacity', value[0])}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>Transparent</span>
                    <span>Solid</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Advanced Options */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Layers className="h-4 w-4" />
                  Advanced Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  variant="outline"
                  className="w-full justify-between"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                >
                  Advanced Settings
                  {showAdvanced ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>

                {showAdvanced && (
                  <div className="space-y-4 pt-2">
                    <div>
                      <Label className="mb-2 block">Animation Style</Label>
                      <Select
                        value={config.animationStyle || 'smooth'}
                        onValueChange={(value) => onChange('animationStyle', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="smooth">Smooth</SelectItem>
                          <SelectItem value="bouncy">Bouncy</SelectItem>
                          <SelectItem value="sharp">Sharp</SelectItem>
                          <SelectItem value="none">None</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="mb-2 block">Theme Mode</Label>
                      <div className="grid grid-cols-3 gap-2">
                        {[
                          { value: 'light', icon: Sun, label: 'Light' },
                          { value: 'dark', icon: Moon, label: 'Dark' },
                          { value: 'auto', icon: Monitor, label: 'Auto' }
                        ].map((theme) => (
                          <Button
                            key={theme.value}
                            variant={config.theme === theme.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => onChange('theme', theme.value)}
                            className="flex items-center gap-1"
                          >
                            <theme.icon className="h-3 w-3" />
                            {theme.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
