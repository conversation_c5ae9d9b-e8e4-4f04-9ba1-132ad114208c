import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  RotateCw, 
  Eye,
  Maximize2,
  Minimize2,
  Wifi,
  Battery,
  Signal
} from "lucide-react";
import { cn } from "@/lib/utils";
import ModernWidgetPreview from "./ModernWidgetPreview";

interface DevicePreviewProps {
  config: any;
  showDeviceFrame?: boolean;
  showControls?: boolean;
  className?: string;
}

export const DevicePreview: React.FC<DevicePreviewProps> = ({ 
  config, 
  showDeviceFrame = true,
  showControls = true,
  className 
}) => {
  const [activeDevice, setActiveDevice] = useState("desktop");
  const [orientation, setOrientation] = useState("portrait");
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Enhanced device configurations with realistic specs
  const devices = {
    desktop: {
      name: "Desktop",
      icon: Monitor,
      width: "100%",
      height: "600px",
      frame: "border-t-4 rounded-t-lg border-slate-300 bg-slate-100",
      scale: 1,
      canRotate: false,
      specs: "1920×1080"
    },
    tablet: {
      name: "iPad",
      icon: Tablet,
      width: orientation === "portrait" ? "768px" : "1024px",
      height: orientation === "portrait" ? "1024px" : "768px",
      frame: "border-[12px] rounded-[28px] border-slate-800 bg-slate-900",
      scale: 0.45,
      canRotate: true,
      specs: orientation === "portrait" ? "768×1024" : "1024×768"
    },
    mobile: {
      name: "iPhone",
      icon: Smartphone,
      width: orientation === "portrait" ? "375px" : "667px",
      height: orientation === "portrait" ? "812px" : "375px",
      frame: "border-[8px] rounded-[32px] border-slate-900 bg-slate-900",
      scale: 0.65,
      canRotate: true,
      specs: orientation === "portrait" ? "375×812" : "667×375"
    }
  };

  const currentDevice = devices[activeDevice as keyof typeof devices];

  const toggleOrientation = () => {
    setOrientation(prev => prev === "portrait" ? "landscape" : "portrait");
  };

  const toggleFullscreen = () => {
    setIsFullscreen(prev => !prev);
  };

  // Mobile status bar component
  const MobileStatusBar = () => (
    <div className="h-6 bg-black flex items-center justify-between px-4 text-white text-xs">
      <div className="flex items-center gap-1">
        <span className="font-medium">9:41</span>
      </div>
      <div className="flex items-center gap-1">
        <Signal className="h-3 w-3" />
        <Wifi className="h-3 w-3" />
        <Battery className="h-3 w-3" />
      </div>
    </div>
  );

  // Tablet status bar component
  const TabletStatusBar = () => (
    <div className="h-8 bg-slate-100 dark:bg-slate-800 flex items-center justify-between px-6 text-sm">
      <div className="flex items-center gap-2">
        <span className="font-medium">9:41 AM</span>
      </div>
      <div className="flex items-center gap-2">
        <Wifi className="h-4 w-4" />
        <Battery className="h-4 w-4" />
      </div>
    </div>
  );

  return (
    <Card className={cn(
      "transition-all duration-300",
      isFullscreen && "fixed inset-4 z-50 h-auto",
      className
    )}>
      {showControls && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Device Preview
              </CardTitle>
              <Badge variant="outline" className="font-normal">
                {config.behavior?.position || "bottom-right"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              {currentDevice.canRotate && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleOrientation}
                  className="flex items-center gap-1"
                >
                  <RotateCw className="h-3 w-3" />
                  {orientation === "portrait" ? "Landscape" : "Portrait"}
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={toggleFullscreen}
                className="flex items-center gap-1"
              >
                {isFullscreen ? (
                  <>
                    <Minimize2 className="h-3 w-3" />
                    Exit
                  </>
                ) : (
                  <>
                    <Maximize2 className="h-3 w-3" />
                    Fullscreen
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
      )}
      
      <CardContent className="space-y-4">
        {showControls && (
          <Tabs value={activeDevice} onValueChange={setActiveDevice}>
            <TabsList className="grid w-full grid-cols-3">
              {Object.entries(devices).map(([key, device]) => {
                const Icon = device.icon;
                return (
                  <TabsTrigger 
                    key={key} 
                    value={key} 
                    className="flex items-center gap-2"
                  >
                    <Icon className="h-4 w-4" />
                    {device.name}
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </Tabs>
        )}

        {/* Device Preview */}
        <div className="flex justify-center items-center min-h-[400px] p-4">
          <div
            className={cn(
              "overflow-hidden transition-all duration-300 relative",
              showDeviceFrame && currentDevice.frame,
              activeDevice === "desktop" && "w-full"
            )}
            style={{
              width: activeDevice === "desktop" ? "100%" : currentDevice.width,
              height: currentDevice.height,
              transform: activeDevice !== "desktop" ? `scale(${currentDevice.scale})` : undefined,
              maxWidth: "100%",
              maxHeight: isFullscreen ? "calc(100vh - 200px)" : "600px"
            }}
          >
            {/* Device-specific status bars */}
            {showDeviceFrame && activeDevice === "mobile" && <MobileStatusBar />}
            {showDeviceFrame && activeDevice === "tablet" && <TabletStatusBar />}
            
            {/* Widget Preview Content */}
            <div 
              className="w-full h-full relative"
              style={{
                height: showDeviceFrame && activeDevice !== "desktop" 
                  ? `calc(100% - ${activeDevice === "mobile" ? "24px" : "32px"})` 
                  : "100%"
              }}
            >
              <div 
                className="w-full h-full"
                style={{
                  transform: activeDevice !== "desktop" ? `scale(${1 / currentDevice.scale})` : undefined,
                  transformOrigin: "top left",
                  width: activeDevice !== "desktop" ? `${currentDevice.scale * 100}%` : "100%",
                  height: activeDevice !== "desktop" ? `${currentDevice.scale * 100}%` : "100%"
                }}
              >
                <ModernWidgetPreview 
                  config={config} 
                  deviceType={activeDevice as 'desktop' | 'tablet' | 'mobile'} 
                />
              </div>
            </div>

            {/* Device home indicator for mobile */}
            {showDeviceFrame && activeDevice === "mobile" && (
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2">
                <div className="w-32 h-1 bg-white rounded-full opacity-60"></div>
              </div>
            )}
          </div>
        </div>

        {/* Device Info */}
        {showControls && (
          <div className="flex justify-center">
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <span className="font-medium">Resolution:</span>
                <span>{currentDevice.specs}</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="font-medium">Scale:</span>
                <span>{Math.round(currentDevice.scale * 100)}%</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="font-medium">Orientation:</span>
                <span className="capitalize">{orientation}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
