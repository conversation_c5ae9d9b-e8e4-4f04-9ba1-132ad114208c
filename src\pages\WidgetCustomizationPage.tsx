import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { WidgetPreview } from "@/components/widget-configurator/WidgetPreview";
import { WidgetAppearanceTab } from "@/components/widget-configurator/WidgetAppearanceTab";
import { WidgetBehaviorTab } from "@/components/widget-configurator/WidgetBehaviorTab";
import { WidgetContentTab } from "@/components/widget-configurator/WidgetContentTab";
import { WidgetEmbeddingTab } from "@/components/widget-configurator/WidgetEmbeddingTab";
import { WidgetTestingPlatform } from "@/components/widget-configurator/testing/WidgetTestingPlatform";
import WidgetThemeSelector from "@/components/widget-configurator/WidgetThemeSelector";
import WidgetAnimationSelector from "@/components/widget-configurator/WidgetAnimationSelector";
import WidgetIconSelector from "@/components/widget-configurator/WidgetIconSelector";
import WidgetPositionSelector from "@/components/widget-configurator/WidgetPositionSelector";
import { Save, ArrowLeft } from "lucide-react";

const WidgetCustomizationPage: React.FC = () => {
    const [widgetName, setWidgetName] = useState("My Chat Widget");
    const [widgetId] = useState(`widget-${Date.now()}`);
    const [activeTab, setActiveTab] = useState("appearance");

    // Widget configuration state
    const [config, setConfig] = useState({
        appearance: {
            theme: "Modern Blue",
            primaryColor: "#3b82f6",
            secondaryColor: "#ffffff",
            borderRadius: 8,
            chatIconSize: 50,
            fontFamily: "inter",
            icon: "MessageCircle"
        },
        behavior: {
            position: "bottom-right" as "bottom-right" | "bottom-left" | "top-left" | "top-right",
            animation: "fade" as "fade" | "slide" | "bounce" | "scale" | "none",
            autoOpen: "no",
            delay: 3,
            mobileBehavior: "responsive",
            showAfterPageViews: 1
        },
        content: {
            welcomeMessage: "Hello! How can I help you today?",
            botName: "Support Agent",
            inputPlaceholder: "Type your message here...",
            chatButtonText: "Chat with us",
            headerTitle: "Customer Support",
            showAvatar: true,
            showTypingIndicator: true,
            enablePreChatForm: false,
            preChatFormFields: ["name", "email"],
            preChatFormRequired: true,
            enableFeedback: true,
            feedbackPosition: "end-chat"
        },
        embedding: {
            allowedDomains: "*"
        }
    });

    // Update configuration values
    const handleAppearanceChange = (key: string, value: any) => {
        setConfig({
            ...config,
            appearance: {
                ...config.appearance,
                [key]: value
            }
        });
    };

    const handleBehaviorChange = (key: string, value: any) => {
        setConfig({
            ...config,
            behavior: {
                ...config.behavior,
                [key]: value
            }
        });
    };

    const handleContentChange = (key: string, value: any) => {
        setConfig({
            ...config,
            content: {
                ...config.content,
                [key]: value
            }
        });
    };

    const handleEmbeddingChange = (key: string, value: any) => {
        setConfig({
            ...config,
            embedding: {
                ...config.embedding,
                [key]: value
            }
        });
    };

    const handleThemeChange = (theme: string) => {
        handleAppearanceChange('theme', theme);

        // Update colors based on theme
        const selectedTheme = theme === "Modern Blue"
            ? { primaryColor: "#3b82f6", secondaryColor: "#ffffff" }
            : theme === "Sleek Dark"
                ? { primaryColor: "#6366f1", secondaryColor: "#1e1e2e" }
                : theme === "Forest Green"
                    ? { primaryColor: "#10b981", secondaryColor: "#f8fafc" }
                    : theme === "Sunset Orange"
                        ? { primaryColor: "#f97316", secondaryColor: "#ffffff" }
                        : { primaryColor: "#3b82f6", secondaryColor: "#ffffff" };

        handleAppearanceChange('primaryColor', selectedTheme.primaryColor);
        handleAppearanceChange('secondaryColor', selectedTheme.secondaryColor);
    };

    const handlePositionChange = (position: "bottom-right" | "bottom-left" | "top-left" | "top-right") => {
        handleBehaviorChange('position', position);
    };

    const handleAnimationChange = (animation: "fade" | "slide" | "bounce" | "scale" | "none") => {
        handleBehaviorChange('animation', animation);
    };

    const handleIconChange = (icon: string) => {
        handleAppearanceChange('icon', icon);
    };

    const handleSave = () => {
        console.log("Saving widget configuration:", config);
        // Implement actual save logic here
    };

    return (
        <div className="container mx-auto py-6 space-y-6">
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="icon">
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold">Widget Customization</h1>
                        <p className="text-gray-500">Configure and preview your chat widget</p>
                    </div>
                </div>
                <Button onClick={handleSave}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Configuration Panel */}
                <div className="lg:col-span-2 space-y-6">
                    <Card>
                        <CardHeader className="pb-3 border-b">
                            <div className="flex justify-between items-center">
                                <div className="space-y-1">
                                    <Label htmlFor="widget-name">Widget Name</Label>
                                    <Input
                                        id="widget-name"
                                        value={widgetName}
                                        onChange={(e) => setWidgetName(e.target.value)}
                                        className="max-w-sm"
                                    />
                                </div>
                                <div className="text-sm text-gray-500">
                                    ID: {widgetId}
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="p-0">
                            <Tabs value={activeTab} onValueChange={setActiveTab}>
                                <TabsList className="w-full justify-start rounded-none border-b h-auto bg-transparent p-0">
                                    <TabsTrigger
                                        value="appearance"
                                        className="rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-blue-600 data-[state=active]:text-blue-600 px-4 py-2"
                                    >
                                        Appearance
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="behavior"
                                        className="rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-blue-600 data-[state=active]:text-blue-600 px-4 py-2"
                                    >
                                        Behavior
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="content"
                                        className="rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-blue-600 data-[state=active]:text-blue-600 px-4 py-2"
                                    >
                                        Content
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="embedding"
                                        className="rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-blue-600 data-[state=active]:text-blue-600 px-4 py-2"
                                    >
                                        Embedding
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="testing"
                                        className="rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-blue-600 data-[state=active]:text-blue-600 px-4 py-2"
                                    >
                                        Testing
                                    </TabsTrigger>
                                </TabsList>

                                <div className="p-6">
                                    <TabsContent value="appearance" className="m-0">
                                        <div className="space-y-6">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div className="space-y-6">
                                                    <WidgetThemeSelector
                                                        selectedTheme={config.appearance.theme}
                                                        onThemeChange={handleThemeChange}
                                                    />
                                                    <WidgetIconSelector
                                                        selectedIcon={config.appearance.icon}
                                                        onIconChange={handleIconChange}
                                                    />
                                                </div>
                                                <div className="space-y-6">
                                                    <WidgetPositionSelector
                                                        selectedPosition={config.behavior.position}
                                                        onPositionChange={handlePositionChange}
                                                    />
                                                    <WidgetAnimationSelector
                                                        selectedAnimation={config.behavior.animation}
                                                        onAnimationChange={handleAnimationChange}
                                                    />
                                                </div>
                                            </div>
                                            <WidgetAppearanceTab
                                                config={config.appearance}
                                                onChange={handleAppearanceChange}
                                            />
                                        </div>
                                    </TabsContent>

                                    <TabsContent value="behavior" className="m-0">
                                        <WidgetBehaviorTab
                                            config={config.behavior}
                                            onChange={handleBehaviorChange}
                                        />
                                    </TabsContent>

                                    <TabsContent value="content" className="m-0">
                                        <WidgetContentTab
                                            config={config.content}
                                            onChange={handleContentChange}
                                        />
                                    </TabsContent>

                                    <TabsContent value="embedding" className="m-0">
                                        <WidgetEmbeddingTab
                                            config={config.embedding}
                                            widgetId={widgetId}
                                            onChange={handleEmbeddingChange}
                                        />
                                    </TabsContent>

                                    <TabsContent value="testing" className="m-0">
                                        <WidgetTestingPlatform
                                            config={config}
                                            widgetId={widgetId}
                                        />
                                    </TabsContent>
                                </div>
                            </Tabs>
                        </CardContent>
                    </Card>
                </div>

                {/* Preview Panel */}
                <div className="space-y-6">
                    <WidgetPreview config={config} />

                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <Button className="w-full" variant="outline">
                                Reset to Defaults
                            </Button>
                            <Button className="w-full" variant="outline">
                                Save as Template
                            </Button>
                            <Button className="w-full" variant="outline">
                                Preview in New Tab
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
};

export default WidgetCustomizationPage; 