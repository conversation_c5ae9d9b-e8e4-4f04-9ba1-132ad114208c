import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  ModelCard,
  ModelFilters,
  ModelMetricsCard,
  ModelTrainingForm,
  ModelVersionHistory,
  ModelDeploymentSettings,
} from "@/components/ai-model-management";
import { Brain, Layers, Settings, BarChart3, GitBranch, Rocket } from "lucide-react";

/**
 * Model Components Demo Page
 * 
 * This page demonstrates all the AI model management components
 * individually for testing and development purposes.
 */
const ModelComponentsDemo: React.FC = () => {
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");

  // Mock data
  const mockModels = [
    {
      id: "1",
      name: "Customer Sentiment Analyzer",
      description: "Advanced NLP model for analyzing customer feedback sentiment with high accuracy",
      status: "active" as const,
      type: "Classification",
      version: "v2.1.0",
      lastUpdated: "2024-01-15",
      accuracy: 94.2,
    },
    {
      id: "2",
      name: "Product Recommendation Engine",
      description: "ML model for personalized product recommendations based on user behavior",
      status: "training" as const,
      type: "Recommendation",
      version: "v1.3.0",
      lastUpdated: "2024-01-14",
      trainingProgress: 75,
    },
    {
      id: "3",
      name: "Fraud Detection Model",
      description: "Real-time fraud detection for financial transactions with low false positives",
      status: "error" as const,
      type: "Classification",
      version: "v1.0.2",
      lastUpdated: "2024-01-13",
      accuracy: 89.7,
    },
    {
      id: "4",
      name: "Image Classification Model",
      description: "Deep learning model for image classification and object detection",
      status: "draft" as const,
      type: "Computer Vision",
      version: "v0.9.0",
      lastUpdated: "2024-01-12",
      accuracy: 87.3,
    },
  ];

  const mockVersions = [
    {
      id: "v2.1.0",
      version: "v2.1.0",
      date: "2024-01-15",
      accuracy: 94.2,
      status: "active" as const,
      size: "245 MB",
      commitMessage: "Improved accuracy with enhanced feature engineering",
    },
    {
      id: "v2.0.0",
      version: "v2.0.0",
      date: "2024-01-10",
      accuracy: 92.8,
      status: "archived" as const,
      size: "238 MB",
      commitMessage: "Major architecture update with transformer layers",
    },
    {
      id: "v1.9.0",
      version: "v1.9.0",
      date: "2024-01-05",
      accuracy: 91.5,
      status: "archived" as const,
      size: "232 MB",
      commitMessage: "Performance optimizations and bug fixes",
    },
  ];

  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId);
    console.log("Selected model:", modelId);
  };

  const clearFilters = () => {
    setSearch("");
    setStatusFilter("");
    setTypeFilter("");
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-slate-800">AI Model Management Components</h1>
        <p className="text-slate-600">
          Demonstration of all available model management components
        </p>
        <div className="flex justify-center gap-2">
          <Badge variant="outline" className="gap-1">
            <Brain className="h-3 w-3" />
            6 Components
          </Badge>
          <Badge variant="outline" className="gap-1">
            <Layers className="h-3 w-3" />
            Production Ready
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="model-cards" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="model-cards" className="text-xs">
            <Layers className="mr-1 h-3.5 w-3.5" />
            Model Cards
          </TabsTrigger>
          <TabsTrigger value="filters" className="text-xs">
            <Settings className="mr-1 h-3.5 w-3.5" />
            Filters
          </TabsTrigger>
          <TabsTrigger value="metrics" className="text-xs">
            <BarChart3 className="mr-1 h-3.5 w-3.5" />
            Metrics
          </TabsTrigger>
          <TabsTrigger value="training" className="text-xs">
            <Brain className="mr-1 h-3.5 w-3.5" />
            Training
          </TabsTrigger>
          <TabsTrigger value="versions" className="text-xs">
            <GitBranch className="mr-1 h-3.5 w-3.5" />
            Versions
          </TabsTrigger>
          <TabsTrigger value="deployment" className="text-xs">
            <Rocket className="mr-1 h-3.5 w-3.5" />
            Deployment
          </TabsTrigger>
        </TabsList>

        <TabsContent value="model-cards" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Model Cards Component</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {mockModels.map((model) => (
                  <ModelCard
                    key={model.id}
                    {...model}
                    onSelect={handleModelSelect}
                  />
                ))}
              </div>
              {selectedModel && (
                <div className="mt-4 p-3 bg-blue-50 rounded-md">
                  <p className="text-sm text-blue-800">
                    Selected Model ID: <strong>{selectedModel}</strong>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="filters" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Model Filters Component</CardTitle>
            </CardHeader>
            <CardContent>
              <ModelFilters
                search={search}
                setSearch={setSearch}
                statusFilter={statusFilter}
                setStatusFilter={setStatusFilter}
                typeFilter={typeFilter}
                setTypeFilter={setTypeFilter}
                clearFilters={clearFilters}
              />
              <div className="mt-4 p-3 bg-gray-50 rounded-md">
                <p className="text-sm text-gray-700">
                  <strong>Current Filters:</strong> Search: "{search}", Status: "{statusFilter}", Type: "{typeFilter}"
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ModelMetricsCard modelId="1" />
            <ModelMetricsCard modelId="2" />
          </div>
        </TabsContent>

        <TabsContent value="training" className="space-y-6">
          <ModelTrainingForm
            onSubmit={(data) => {
              console.log("Training form submitted:", data);
              alert("Training form submitted! Check console for data.");
            }}
          />
        </TabsContent>

        <TabsContent value="versions" className="space-y-6">
          <ModelVersionHistory
            versions={mockVersions}
            onCompare={(versions) => {
              console.log("Compare versions:", versions);
              alert(`Comparing versions: ${versions.join(", ")}`);
            }}
            onRevert={(versionId) => {
              console.log("Revert to version:", versionId);
              alert(`Reverting to version: ${versionId}`);
            }}
            onDownload={(versionId) => {
              console.log("Download version:", versionId);
              alert(`Downloading version: ${versionId}`);
            }}
          />
        </TabsContent>

        <TabsContent value="deployment" className="space-y-6">
          <ModelDeploymentSettings
            modelId="1"
            onSave={(settings) => {
              console.log("Save deployment settings:", settings);
              alert("Deployment settings saved! Check console for data.");
            }}
            onDeploy={() => {
              console.log("Deploy model");
              alert("Model deployment initiated!");
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ModelComponentsDemo;
