import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DeviceSimulator } from "./DeviceSimulator";
import { LiveEmbedPreview } from "./LiveEmbedPreview";
import { PerformanceMetrics } from "./PerformanceMetrics";
import { TestScenarios } from "./TestScenarios";
import { TestEnvironmentSelector } from "./TestEnvironmentSelector";
import { Button } from "@/components/ui/button";
import { Download, Share2, Code, Monitor, Smartphone, Wrench } from "lucide-react";

interface WidgetTestingContainerProps {
    widgetId: string;
    config: any;
    initialTab?: string;
}

export function WidgetTestingContainer({
    widgetId,
    config,
    initialTab = "preview"
}: WidgetTestingContainerProps) {
    const [activeTab, setActiveTab] = useState(initialTab);
    const [environment, setEnvironment] = useState("development");
    const [previewUrl, setPreviewUrl] = useState(
        `https://${environment}-preview.chatadmin.com/preview/${widgetId}?t=${Date.now()}`
    );

    const handleEnvironmentChange = (env: string) => {
        setEnvironment(env);
        setPreviewUrl(
            `https://${env}-preview.chatadmin.com/preview/${widgetId}?t=${Date.now()}`
        );
    };

    // Navigation options
    const navigationTabs = [
        { id: "preview", label: "Live Preview", icon: <Monitor className="h-4 w-4 mr-2" /> },
        { id: "devices", label: "Device Testing", icon: <Smartphone className="h-4 w-4 mr-2" /> },
        { id: "embed", label: "Embed Code", icon: <Code className="h-4 w-4 mr-2" /> },
        { id: "testing", label: "Test Tools", icon: <Wrench className="h-4 w-4 mr-2" /> }
    ];

    // Generate embed code based on settings
    const generateEmbedCode = () => {
        return `<!-- Chat Widget Embed Code -->
<script>
  (function(w,d,s,o,f,js,fjs){
    w['ChatWidget']=o;w[o]=w[o]||function(){(w[o].q=w[o].q||[]).push(arguments)};
    w[o].l=1*new Date();js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
    js.id=o;js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
  }(window,document,'script','chatWidget','https://${environment}.chatadmin.com/widget/${widgetId}.js'));
  
  chatWidget('init', ${JSON.stringify(config, null, 2)});
</script>`;
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Widget Testing & Embed</h2>
                <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export Report
                    </Button>
                    <Button variant="outline" size="sm">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share Results
                    </Button>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                    <Card>
                        <CardHeader className="border-b pb-2">
                            <Tabs
                                value={activeTab}
                                onValueChange={setActiveTab}
                                className="w-full"
                            >
                                <TabsList className="grid grid-cols-4">
                                    {navigationTabs.map(tab => (
                                        <TabsTrigger key={tab.id} value={tab.id} className="flex items-center">
                                            {tab.icon}
                                            {tab.label}
                                        </TabsTrigger>
                                    ))}
                                </TabsList>
                            </Tabs>
                        </CardHeader>
                        <CardContent className="p-0">
                            <TabsContent value="preview" className="m-0">
                                <div className="p-6">
                                    <LiveEmbedPreview
                                        config={config}
                                        widgetId={widgetId}
                                        environment={environment}
                                    />
                                </div>
                            </TabsContent>

                            <TabsContent value="devices" className="m-0">
                                <div className="p-6">
                                    <DeviceSimulator previewUrl={previewUrl} />
                                </div>
                            </TabsContent>

                            <TabsContent value="embed" className="m-0">
                                <div className="p-6">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Embed Code</CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <pre className="bg-slate-100 p-4 rounded-md overflow-x-auto text-sm">
                                                {generateEmbedCode()}
                                            </pre>
                                            <div className="mt-4">
                                                <Button variant="secondary" className="w-full" onClick={() => {
                                                    navigator.clipboard.writeText(generateEmbedCode());
                                                }}>
                                                    Copy Embed Code
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </TabsContent>

                            <TabsContent value="testing" className="m-0">
                                <div className="p-6">
                                    <TestScenarios
                                        onRunTest={(scenarioId) => console.log(`Running test: ${scenarioId}`)}
                                        previewUrl={previewUrl}
                                    />
                                </div>
                            </TabsContent>
                        </CardContent>
                    </Card>
                </div>

                <div className="space-y-6">
                    <TestEnvironmentSelector
                        environment={environment}
                        onEnvironmentChange={handleEnvironmentChange}
                    />
                    <PerformanceMetrics previewUrl={previewUrl} />
                </div>
            </div>
        </div>
    );
} 