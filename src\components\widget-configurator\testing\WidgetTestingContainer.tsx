import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DeviceSimulator } from "./DeviceSimulator";
import { LiveEmbedPreview } from "./LiveEmbedPreview";
import { PerformanceMetrics } from "./PerformanceMetrics";
import { TestScenarios } from "./TestScenarios";
import { TestEnvironmentSelector } from "./TestEnvironmentSelector";
import { Button } from "@/components/ui/button";
import { Download, Share2, Code, Monitor, Smartphone, Wrench } from "lucide-react";

interface WidgetTestingContainerProps {
    widgetId: string;
    config: any;
    initialTab?: string;
}

export function WidgetTestingContainer({
    widgetId,
    config,
    initialTab = "preview"
}: WidgetTestingContainerProps) {
    const [activeTab, setActiveTab] = useState(initialTab);
    const [environment, setEnvironment] = useState("development");
    const [previewUrl, setPreviewUrl] = useState(
        `https://${environment}-preview.chatadmin.com/preview/${widgetId}?t=${Date.now()}`
    );

    const handleEnvironmentChange = (env: string) => {
        setEnvironment(env);
        setPreviewUrl(
            `https://${env}-preview.chatadmin.com/preview/${widgetId}?t=${Date.now()}`
        );
    };

    // Navigation options
    const navigationTabs = [
        { id: "preview", label: "Live Preview", icon: <Monitor className="h-4 w-4 mr-2" /> },
        { id: "devices", label: "Device Testing", icon: <Smartphone className="h-4 w-4 mr-2" /> },
        { id: "embed", label: "Embed Code", icon: <Code className="h-4 w-4 mr-2" /> },
        { id: "testing", label: "Test Tools", icon: <Wrench className="h-4 w-4 mr-2" /> }
    ];

    // Generate embed code based on settings
    const generateEmbedCode = () => {
        return `<!-- Chat Widget Embed Code -->
<script>
  (function(w,d,s,o,f,js,fjs){
    w['ChatWidget']=o;w[o]=w[o]||function(){(w[o].q=w[o].q||[]).push(arguments)};
    w[o].l=1*new Date();js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
    js.id=o;js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
  }(window,document,'script','chatWidget','https://${environment}.chatadmin.com/widget/${widgetId}.js'));
  
  chatWidget('init', ${JSON.stringify(config, null, 2)});
</script>`;
    };

    // Get URL to the custom test page
    const getTestPageUrl = () => {
        const baseUrl = window.location.origin;
        return `${baseUrl}/widget-test-page.html`;
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Widget Testing & Embed</h2>
                <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export Report
                    </Button>
                    <Button variant="outline" size="sm">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share Results
                    </Button>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                    <Card>
                        <CardHeader className="border-b pb-2">
                            <h3 className="text-lg font-medium">Widget Testing Platform</h3>
                        </CardHeader>
                        <CardContent className="p-0">
                            <Tabs
                                value={activeTab}
                                onValueChange={setActiveTab}
                                className="w-full"
                            >
                                <div className="px-6 pt-6">
                                    <TabsList className="grid grid-cols-4">
                                        {navigationTabs.map(tab => (
                                            <TabsTrigger key={tab.id} value={tab.id} className="flex items-center">
                                                {tab.icon}
                                                {tab.label}
                                            </TabsTrigger>
                                        ))}
                                    </TabsList>
                                </div>

                                <TabsContent value="preview" className="m-0">
                                    <div className="p-6">
                                        <LiveEmbedPreview
                                            config={config}
                                            widgetId={widgetId}
                                            environment={environment}
                                        />
                                    </div>
                                </TabsContent>

                                <TabsContent value="devices" className="m-0">
                                    <div className="p-6">
                                        <DeviceSimulator previewUrl={getTestPageUrl()} />
                                    </div>
                                </TabsContent>

                                <TabsContent value="embed" className="m-0">
                                    <div className="p-6">
                                        <Card>
                                            <CardHeader>
                                                <CardTitle>Embed Code</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="relative">
                                                    <pre className="bg-slate-900 text-white p-4 rounded-md overflow-x-auto text-sm font-mono whitespace-pre-wrap">
                                                        <code>
                                                            <span className="text-slate-400">&#x3C;!-- Chat Widget Embed Code --&#x3E;</span>
                                                            <br />
                                                            <span className="text-slate-400">&#x3C;script&#x3E;</span>
                                                            <br />
                                                            <span className="text-cyan-400">  (function</span>
                                                            <span className="text-pink-400">(</span>
                                                            <span className="text-orange-400">w,d,s,o,f,js,fjs</span>
                                                            <span className="text-pink-400">)</span>
                                                            <span className="text-cyan-400">{`{`}</span>
                                                            <br />
                                                            <span className="text-slate-300">    w</span>
                                                            <span className="text-pink-400">[</span>
                                                            <span className="text-green-400">'ChatWidget'</span>
                                                            <span className="text-pink-400">]</span>
                                                            <span className="text-slate-300">=o; w</span>
                                                            <span className="text-pink-400">[</span>
                                                            <span className="text-slate-300">o</span>
                                                            <span className="text-pink-400">]</span>
                                                            <span className="text-slate-300">=w</span>
                                                            <span className="text-pink-400">[</span>
                                                            <span className="text-slate-300">o</span>
                                                            <span className="text-pink-400">]</span>
                                                            <span className="text-cyan-400">||function</span>
                                                            <span className="text-pink-400">()</span>
                                                            <span className="text-cyan-400">{`{`}</span>
                                                            <span className="text-pink-400">(</span>
                                                            <span className="text-slate-300">w</span>
                                                            <span className="text-pink-400">[</span>
                                                            <span className="text-slate-300">o</span>
                                                            <span className="text-pink-400">]</span>
                                                            <span className="text-slate-300">.q=w</span>
                                                            <span className="text-pink-400">[</span>
                                                            <span className="text-slate-300">o</span>
                                                            <span className="text-pink-400">]</span>
                                                            <span className="text-slate-300">.q||</span>
                                                            <span className="text-pink-400">[])</span>
                                                            <span className="text-slate-300">.push</span>
                                                            <span className="text-pink-400">(</span>
                                                            <span className="text-slate-300">arguments</span>
                                                            <span className="text-pink-400">)</span>
                                                            <span className="text-cyan-400">{`}`}</span>
                                                            <span className="text-slate-300">;</span>
                                                            <br />
                                                            <span className="text-slate-300">    w</span>
                                                            <span className="text-pink-400">[</span>
                                                            <span className="text-slate-300">o</span>
                                                            <span className="text-pink-400">]</span>
                                                            <span className="text-slate-300">.l=</span>
                                                            <span className="text-yellow-400">1</span>
                                                            <span className="text-slate-300">*</span>
                                                            <span className="text-cyan-400">new</span>
                                                            <span className="text-slate-300"> Date</span>
                                                            <span className="text-pink-400">()</span>
                                                            <span className="text-slate-300">; js=d.createElement</span>
                                                            <span className="text-pink-400">(</span>
                                                            <span className="text-slate-300">s</span>
                                                            <span className="text-pink-400">)</span>
                                                            <span className="text-slate-300">,fjs=d.getElementsByTagName</span>
                                                            <span className="text-pink-400">(</span>
                                                            <span className="text-slate-300">s</span>
                                                            <span className="text-pink-400">)[</span>
                                                            <span className="text-yellow-400">0</span>
                                                            <span className="text-pink-400">]</span>
                                                            <span className="text-slate-300">;</span>
                                                            <br />
                                                            <span className="text-slate-300">    js.id=o; js.src=f; js.async=</span>
                                                            <span className="text-yellow-400">1</span>
                                                            <span className="text-slate-300">; fjs.parentNode.insertBefore</span>
                                                            <span className="text-pink-400">(</span>
                                                            <span className="text-slate-300">js,fjs</span>
                                                            <span className="text-pink-400">)</span>
                                                            <span className="text-slate-300">;</span>
                                                            <br />
                                                            <span className="text-cyan-400">  {`}`}</span>
                                                            <span className="text-pink-400">(</span>
                                                            <span className="text-slate-300">window,document,</span>
                                                            <span className="text-green-400">'script'</span>
                                                            <span className="text-slate-300">,</span>
                                                            <span className="text-green-400">'chatWidget'</span>
                                                            <span className="text-slate-300">,</span>
                                                            <span className="text-green-400">{`'https://${environment}.chatadmin.com/widget/${widgetId}.js'`}</span>
                                                            <span className="text-pink-400">))</span>
                                                            <span className="text-slate-300">;</span>
                                                            <br />
                                                            <br />
                                                            <span className="text-slate-300">  chatWidget</span>
                                                            <span className="text-pink-400">(</span>
                                                            <span className="text-green-400">'init'</span>
                                                            <span className="text-slate-300">, </span>
                                                            <span
                                                                dangerouslySetInnerHTML={{
                                                                    __html: JSON.stringify(config, null, 2)
                                                                        .split('\n')
                                                                        .map((line, i) => {
                                                                            const isFirst = i === 0;
                                                                            const isLast = i === JSON.stringify(config, null, 2).split('\n').length - 1;
                                                                            const hasColon = line.includes(':');
                                                                            const hasQuotes = line.includes('"');
                                                                            const isBoolean = line.includes('true') || line.includes('false');
                                                                            const isNumber = hasColon && !isNaN(Number(line.split(':')[1].trim()));

                                                                            let colorClass = 'text-slate-300';
                                                                            if (isFirst || isLast) {
                                                                                colorClass = 'text-yellow-400';
                                                                            } else if (hasColon) {
                                                                                if (hasQuotes) {
                                                                                    colorClass = 'text-green-400';
                                                                                } else if (isBoolean || isNumber) {
                                                                                    colorClass = 'text-yellow-400';
                                                                                }
                                                                            }

                                                                            return `<span class="${colorClass}">${line}</span>${i < JSON.stringify(config, null, 2).split('\n').length - 1 ? '<br />' : ''}`;
                                                                        }).join('')
                                                                }}
                                                                className="text-cyan-400"
                                                            />
                                                            <span className="text-pink-400">)</span>
                                                            <span className="text-slate-300">;</span>
                                                            <br />
                                                            <span className="text-slate-400">&#x3C;/script&#x3E;</span>
                                                        </code>
                                                    </pre>

                                                    <Button
                                                        variant="default"
                                                        size="sm"
                                                        className="absolute top-2 right-2 bg-primary hover:bg-primary/90"
                                                        onClick={() => navigator.clipboard.writeText(generateEmbedCode())}
                                                    >
                                                        Copy
                                                    </Button>
                                                </div>

                                                <div className="mt-4">
                                                    <Button variant="outline" className="w-full" onClick={() => {
                                                        const testPageUrl = `${getTestPageUrl()}?widgetId=${widgetId}&env=${environment}`;
                                                        window.open(testPageUrl, '_blank');
                                                    }}>
                                                        Test Embed Code
                                                    </Button>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    </div>
                                </TabsContent>

                                <TabsContent value="testing" className="m-0">
                                    <div className="p-6">
                                        <TestScenarios
                                            onRunTest={(scenarioId) => console.log(`Running test: ${scenarioId}`)}
                                            previewUrl={previewUrl}
                                        />
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>
                </div>

                <div className="space-y-6">
                    <TestEnvironmentSelector
                        environment={environment}
                        onEnvironmentChange={handleEnvironmentChange}
                    />
                    <PerformanceMetrics previewUrl={previewUrl} />
                </div>
            </div>
        </div>
    );
} 