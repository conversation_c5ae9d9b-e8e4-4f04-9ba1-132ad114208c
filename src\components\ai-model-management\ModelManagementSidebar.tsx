import React from "react";
import { Link, useLocation } from "react-router-dom";
import {
  Brain,
  Database,
  Settings,
  BarChart3,
  GitBranch,
  Rocket,
  FileText,
  Activity,
  Layers,
  Cpu,
  Cloud,
  Shield,
  Zap,
  Archive,
  Plus,
  Search,
} from "lucide-react";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ModelManagementSidebarProps {
  onComponentSelect?: (component: string) => void;
  activeComponent?: string;
  showSearch?: boolean;
  showActions?: boolean;
}

export const ModelManagementSidebar: React.FC<ModelManagementSidebarProps> = ({
  onComponentSelect,
  activeComponent,
  showSearch = true,
  showActions = true,
}) => {
  const location = useLocation();

  const isActive = (component: string) => {
    return activeComponent === component || location.pathname.includes(component);
  };

  const handleComponentClick = (component: string) => {
    if (onComponentSelect) {
      onComponentSelect(component);
    }
  };

  const modelManagementItems = [
    {
      label: "Model Overview",
      component: "overview",
      icon: <Brain size={18} />,
      description: "View all models and their status",
      badge: null,
    },
    {
      label: "Model Cards",
      component: "model-cards",
      icon: <Layers size={18} />,
      description: "Browse and manage model cards",
      badge: "12",
    },
    {
      label: "Training Form",
      component: "training-form",
      icon: <Settings size={18} />,
      description: "Create and configure new models",
      badge: null,
    },
    {
      label: "Model Metrics",
      component: "metrics",
      icon: <BarChart3 size={18} />,
      description: "Performance analytics and insights",
      badge: null,
    },
    {
      label: "Version History",
      component: "version-history",
      icon: <GitBranch size={18} />,
      description: "Track model versions and changes",
      badge: "3",
    },
    {
      label: "Deployment",
      component: "deployment",
      icon: <Rocket size={18} />,
      description: "Deploy and manage model instances",
      badge: null,
    },
  ];

  const toolsAndUtilities = [
    {
      label: "Model Filters",
      component: "filters",
      icon: <Search size={18} />,
      description: "Advanced filtering and search",
    },
    {
      label: "Data Sources",
      component: "data-sources",
      icon: <Database size={18} />,
      description: "Manage training datasets",
    },
    {
      label: "Model Registry",
      component: "registry",
      icon: <Archive size={18} />,
      description: "Centralized model storage",
    },
    {
      label: "API Endpoints",
      component: "api-endpoints",
      icon: <Cloud size={18} />,
      description: "Model serving endpoints",
    },
  ];

  const monitoringItems = [
    {
      label: "Performance Monitor",
      component: "performance",
      icon: <Activity size={18} />,
      description: "Real-time model performance",
    },
    {
      label: "Resource Usage",
      component: "resources",
      icon: <Cpu size={18} />,
      description: "CPU, memory, and GPU usage",
    },
    {
      label: "Security & Access",
      component: "security",
      icon: <Shield size={18} />,
      description: "Model access controls",
    },
    {
      label: "Audit Logs",
      component: "audit-logs",
      icon: <FileText size={18} />,
      description: "Model operation history",
    },
  ];

  return (
    <div className="w-full space-y-4">
      {/* Header */}
      <SidebarGroup>
        <div className="flex items-center justify-between px-4 py-2">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold text-slate-800">AI Models</h2>
          </div>
          {showActions && (
            <Button size="sm" variant="outline" className="h-8 w-8 p-0">
              <Plus className="h-4 w-4" />
            </Button>
          )}
        </div>
      </SidebarGroup>

      {/* Search */}
      {showSearch && (
        <SidebarGroup>
          <div className="px-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search models..."
                className="pl-9 h-9"
              />
            </div>
          </div>
        </SidebarGroup>
      )}

      {/* Core Model Management */}
      <SidebarGroup>
        <SidebarGroupLabel className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
          Model Management
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            {modelManagementItems.map((item) => (
              <SidebarMenuItem key={item.component}>
                <SidebarMenuButton
                  isActive={isActive(item.component)}
                  onClick={() => handleComponentClick(item.component)}
                  tooltip={item.description}
                  className="group relative"
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className="flex-shrink-0">
                      {item.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <span className="text-sm font-medium truncate">
                        {item.label}
                      </span>
                    </div>
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      {/* Tools & Utilities */}
      <SidebarGroup>
        <SidebarGroupLabel className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
          Tools & Utilities
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            {toolsAndUtilities.map((item) => (
              <SidebarMenuItem key={item.component}>
                <SidebarMenuButton
                  isActive={isActive(item.component)}
                  onClick={() => handleComponentClick(item.component)}
                  tooltip={item.description}
                  className="group"
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className="flex-shrink-0">
                      {item.icon}
                    </div>
                    <span className="text-sm font-medium truncate">
                      {item.label}
                    </span>
                  </div>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      {/* Monitoring & Analytics */}
      <SidebarGroup>
        <SidebarGroupLabel className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
          Monitoring & Analytics
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            {monitoringItems.map((item) => (
              <SidebarMenuItem key={item.component}>
                <SidebarMenuButton
                  isActive={isActive(item.component)}
                  onClick={() => handleComponentClick(item.component)}
                  tooltip={item.description}
                  className="group"
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className="flex-shrink-0">
                      {item.icon}
                    </div>
                    <span className="text-sm font-medium truncate">
                      {item.label}
                    </span>
                  </div>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      {/* Quick Actions */}
      {showActions && (
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
            Quick Actions
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <div className="px-2 space-y-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start gap-2 h-8"
                onClick={() => handleComponentClick('training-form')}
              >
                <Plus className="h-3.5 w-3.5" />
                New Model
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start gap-2 h-8"
                onClick={() => handleComponentClick('deployment')}
              >
                <Rocket className="h-3.5 w-3.5" />
                Deploy Model
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start gap-2 h-8"
                onClick={() => handleComponentClick('metrics')}
              >
                <Zap className="h-3.5 w-3.5" />
                View Analytics
              </Button>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      )}
    </div>
  );
};
