import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import {
  Monitor,
  Tablet,
  Smartphone,
  RotateCw,
  Eye,
  Maximize2,
  Minimize2
} from "lucide-react";
import { cn } from "@/lib/utils";
import ModernWidgetPreview from "./ModernWidgetPreview";

interface WidgetPreviewProps {
  config: any;
}

export const WidgetPreview: React.FC<WidgetPreviewProps> = ({ config }) => {
  const [activeDevice, setActiveDevice] = useState("desktop");
  const [orientation, setOrientation] = useState("portrait");
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Device configurations
  const devices = {
    desktop: {
      name: "Desktop",
      icon: Monitor,
      width: "100%",
      height: "500px",
      frame: "border-t-4 rounded-t-lg border-slate-300",
      scale: 1,
      canRotate: false
    },
    tablet: {
      name: "Tablet",
      icon: Tablet,
      width: orientation === "portrait" ? "768px" : "1024px",
      height: orientation === "portrait" ? "1024px" : "768px",
      frame: "border-[8px] rounded-[20px] border-slate-800",
      scale: 0.4,
      canRotate: true
    },
    mobile: {
      name: "Mobile",
      icon: Smartphone,
      width: orientation === "portrait" ? "375px" : "667px",
      height: orientation === "portrait" ? "667px" : "375px",
      frame: "border-[6px] rounded-[24px] border-slate-900",
      scale: 0.6,
      canRotate: true
    }
  };

  const currentDevice = devices[activeDevice as keyof typeof devices];

  const toggleOrientation = () => {
    setOrientation(prev => prev === "portrait" ? "landscape" : "portrait");
  };

  const toggleFullscreen = () => {
    setIsFullscreen(prev => !prev);
  };

  return (
    <Card className={cn(
      "transition-all duration-300",
      isFullscreen && "fixed inset-4 z-50 h-auto"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Device Preview
            </CardTitle>
            <Badge variant="outline" className="font-normal">
              {config.behavior?.position || "bottom-right"}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {currentDevice.canRotate && (
              <Button
                variant="outline"
                size="sm"
                onClick={toggleOrientation}
                className="flex items-center gap-1"
              >
                <RotateCw className="h-3 w-3" />
                {orientation === "portrait" ? "Landscape" : "Portrait"}
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullscreen}
              className="flex items-center gap-1"
            >
              {isFullscreen ? (
                <>
                  <Minimize2 className="h-3 w-3" />
                  Exit
                </>
              ) : (
                <>
                  <Maximize2 className="h-3 w-3" />
                  Fullscreen
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Device Selector */}
        <Tabs value={activeDevice} onValueChange={setActiveDevice}>
          <TabsList className="grid w-full grid-cols-3">
            {Object.entries(devices).map(([key, device]) => {
              const Icon = device.icon;
              return (
                <TabsTrigger
                  key={key}
                  value={key}
                  className="flex items-center gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {device.name}
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>

        {/* Device Preview */}
        <div className="flex justify-center items-center min-h-[400px] p-4">
          <div
            className={cn(
              "bg-white overflow-hidden transition-all duration-300",
              currentDevice.frame,
              activeDevice === "desktop" && "w-full"
            )}
            style={{
              width: activeDevice === "desktop" ? "100%" : currentDevice.width,
              height: currentDevice.height,
              transform: activeDevice !== "desktop" ? `scale(${currentDevice.scale})` : undefined,
              maxWidth: "100%",
              maxHeight: isFullscreen ? "calc(100vh - 200px)" : "500px"
            }}
          >
            <div className="w-full h-full relative">
              {/* Device-specific styling wrapper */}
              <div
                className="w-full h-full"
                style={{
                  transform: activeDevice !== "desktop" ? `scale(${1 / currentDevice.scale})` : undefined,
                  transformOrigin: "top left",
                  width: activeDevice !== "desktop" ? `${currentDevice.scale * 100}%` : "100%",
                  height: activeDevice !== "desktop" ? `${currentDevice.scale * 100}%` : "100%"
                }}
              >
                <ModernWidgetPreview
                  config={config}
                  deviceType={activeDevice as 'desktop' | 'tablet' | 'mobile'}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Device Info */}
        <div className="flex justify-center">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <span className="font-medium">Resolution:</span>
              <span>{currentDevice.width} × {currentDevice.height}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-medium">Scale:</span>
              <span>{Math.round(currentDevice.scale * 100)}%</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-medium">Orientation:</span>
              <span className="capitalize">{orientation}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
